import { auth } from "@/auth";
import { NextRequest, NextResponse } from "next/server";
import prisma from "@/lib/prisma";
import { getPlanById } from "@/data/plan";

export async function POST(request: NextRequest) {
  try {
    const session = await auth();
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    const { planId } = await request.json();

    if (!planId) {
      return NextResponse.json(
        { error: "Plan ID is required" },
        { status: 400 }
      );
    }

    // Get the plan details
    const plan = await getPlanById(planId);
    if (!plan || !plan.isActive) {
      return NextResponse.json(
        { error: "Plan not found or inactive" },
        { status: 404 }
      );
    }

    // Don't allow "purchasing" free plans
    if (plan.price === 0) {
      return NextResponse.json(
        { error: "Cannot purchase free plan" },
        { status: 400 }
      );
    }

    // Simulate successful purchase by updating user's plan directly
    const updatedUser = await prisma.user.update({
      where: { id: session.user.id },
      data: {
        currentPlanId: plan.id,
        readingsLimit: plan.readingsLimit,
        readingsUsed: 0, // Reset readings when upgrading
        subscriptionStatus: "ACTIVE",
      },
    });

    // Create a mock subscription record
    await prisma.subscription.create({
      data: {
        userId: session.user.id,
        stripeSubscriptionId: `test_sub_${Date.now()}`,
        stripePriceId: plan.stripePriceId,
        status: "ACTIVE",
        currentPeriodStart: new Date(),
        currentPeriodEnd: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days from now
        cancelAtPeriodEnd: false,
      },
    });

    return NextResponse.json({
      success: true,
      message: `Successfully "purchased" ${plan.nameAr}`,
      plan: {
        id: plan.id,
        name: plan.nameAr,
        readingsLimit: plan.readingsLimit,
      },
      user: {
        readingsLimit: updatedUser.readingsLimit,
        readingsUsed: updatedUser.readingsUsed,
        subscriptionStatus: updatedUser.subscriptionStatus,
      },
    });
  } catch (error) {
    console.error("Error in test purchase:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
