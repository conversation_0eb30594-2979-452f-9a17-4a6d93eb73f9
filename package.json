{"name": "fen<PERSON><PERSON>", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@auth/prisma-adapter": "^2.10.0", "@prisma/client": "^6.12.0", "@radix-ui/react-slot": "^1.2.3", "@stripe/stripe-js": "^7.7.0", "@tabler/icons-react": "^3.34.1", "@tanstack/react-query": "^5.83.0", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.530.0", "motion": "^12.23.11", "next": "15.4.4", "next-auth": "^5.0.0-beta.29", "openai": "^5.10.2", "react": "19.1.0", "react-dom": "19.1.0", "stripe": "^18.3.0", "tailwind-merge": "^3.3.1", "zod": "^4.0.10"}, "devDependencies": {"@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "prisma": "^6.12.0", "tailwindcss": "^4", "tw-animate-css": "^1.3.6", "typescript": "^5"}}