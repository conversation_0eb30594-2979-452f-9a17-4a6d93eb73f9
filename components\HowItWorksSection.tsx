"use client";
import Image from "next/image";

export default function HowItWorksSection() {
  const steps = [
    {
      id: 1,
      imagePath: "/steps/steps-01 1.png",
      alt: "Step 1",
    },
    {
      id: 2,
      imagePath: "/steps/steps-01 2.png",
      alt: "Step 2",
    },
    {
      id: 3,
      imagePath: "/steps/steps-01 3.png",
      alt: "Step 3",
    },
    {
      id: 4,
      imagePath: "/steps/steps-01 4.png",
      alt: "Step 4",
    },
  ];

  return (
    <section className="relative py-20 px-4 overflow-hidden">
      <div className="relative z-10 max-w-7xl mx-auto">
        {/* Section Header */}
        <div className="text-center mb-16">
          <div className="block bg-[#E5B565] px-8 py-3 rounded-full mb-6 mx-auto w-fit">
            <h2 className="text-2xl font-bold">كيف تعمل الخدمة</h2>
          </div>
          <p className="text-[32px] text-gray-700 max-w-2xl mx-auto leading-relaxed">
            عملية بسيطة وسريعة للحصول على قراءة دقيقة
            <br />
            أربع خطوات فقط لمعرفة مستقبلك
          </p>
        </div>

        {/* Steps Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {steps.map((step) => (
            <div key={step.id} className="relative">
              {/* Step Image */}
              <div className="w-full h-auto">
                <Image
                  src={step.imagePath}
                  alt={step.alt}
                  width={300}
                  height={400}
                  className="w-full h-auto object-contain"
                />
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}
