"use client";
import { But<PERSON> } from "@/components/ui/button";
import { IconLogout } from "@tabler/icons-react";
import { handleSignOut } from "@/actions/signout";
import { useState } from "react";

interface SignOutButtonProps {
  className?: string;
  showConfirmation?: boolean;
}

export default function SignOutButton({ 
  className = "w-full justify-start text-gray-400 hover:text-white hover:bg-gray-800",
  showConfirmation = false 
}: SignOutButtonProps) {
  const [isLoading, setIsLoading] = useState(false);

  const handleClick = async () => {
    if (showConfirmation) {
      const confirmed = window.confirm("هل أنت متأكد من تسجيل الخروج؟");
      if (!confirmed) return;
    }

    setIsLoading(true);
    try {
      await handleSignOut();
    } catch (error) {
      console.error("Sign out error:", error);
      setIsLoading(false);
    }
  };

  return (
    <Button
      onClick={handleClick}
      disabled={isLoading}
      variant="ghost"
      className={className}
    >
      <IconLogout className="h-4 w-4 mr-2" />
      {isLoading ? "جاري تسجيل الخروج..." : "تسجيل الخروج"}
    </Button>
  );
}
