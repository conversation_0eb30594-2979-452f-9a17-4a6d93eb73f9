import NextAuth from "next-auth";
import { PrismaAdapter } from "@auth/prisma-adapter";
import authConfig from "./auth.config";
import prisma from "./lib/prisma";
import { getUserById } from "./data//user";
import { getAccountByUserId } from "./data/account";
import {
  assignFreePlanToUser,
  createFreePlanIfNotExists,
} from "./actions/assign-free-plan";

export const {
  auth,
  handlers: { GET, POST },
  signIn,
  signOut,
} = NextAuth({
  adapter: PrismaAdapter(prisma),
  session: { strategy: "jwt" },
  basePath: "/api/auth",
  ...authConfig,
  events: {
    async createUser({ user }) {
      // Ensure free plan exists
      await createFreePlanIfNotExists();

      // Assign free plan to new user
      if (user.id) {
        await assignFreePlanToUser(user.id);
      }
    },
  },
  callbacks: {
    async signIn({ user, account }) {
      if (account?.provider !== "credentials") {
        return true;
      }
      // if (!user.id) return false
      const existingUser = await getUserById(user.id ?? "");

      if (!existingUser?.emailVerified) {
        return false;
      }
      return true;
    },

    async jwt({ token }) {
      if (!token.sub) return token;

      const existingUser = await getUserById(token.sub);

      if (!existingUser) return token;

      const existingAccount = await getAccountByUserId(existingUser.id);

      token.isOauth = !!existingAccount;
      token.name = existingUser.name;
      token.email = existingUser.email;
      token.image = existingUser.image;
      token.role = existingUser.role;
      return token;
    },

    async session({ token, session }) {
      return {
        ...session,
        user: {
          ...session.user,
          id: token.sub,
          isOauth: token.isOauth,
          role: token.role,
        },
      };
    },

    async redirect({ url, baseUrl }) {
      // If the URL is relative, make it absolute
      if (url.startsWith("/")) {
        return `${baseUrl}${url}`;
      }
      // If the URL is on the same origin, allow it
      if (new URL(url).origin === baseUrl) {
        return url;
      }
      // Default redirect to chat for successful authentication
      return `${baseUrl}/chat`;
    },
  },
});
