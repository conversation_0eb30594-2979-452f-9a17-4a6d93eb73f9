"use server";

import prisma from "@/lib/prisma";

export async function createTestPlans() {
  try {
    // Check existing plans
    const existingPlans = await prisma.plan.findMany();
    console.log("Existing plans:", existingPlans.map(p => ({ name: p.name, price: p.price })));

    // Create test plans with simple Stripe price IDs for now
    const testPlans = [
      {
        name: "Starter Plan",
        nameAr: "باقة البداية",
        description: "Perfect for beginners with 5 coffee cup readings",
        descriptionAr: "مثالية للمبتدئين مع 5 قراءات فنجان قهوة",
        price: 3.00,
        currency: "USD",
        interval: "monthly",
        stripePriceId: "price_test_starter", // Temporary test price ID
        features: [
          "5 coffee cup readings per month",
          "Basic symbol analysis",
          "Instant results",
          "Email support"
        ],
        featuresAr: [
          "5 قراءات فنجان قهوة شهرياً",
          "تحليل أساسي للرموز",
          "نتائج فورية",
          "دعم عبر البريد الإلكتروني"
        ],
        readingsLimit: 5,
        isActive: true,
      },
      {
        name: "Basic Plan",
        nameAr: "الباقة الأساسية",
        description: "Most popular plan with 10 detailed readings",
        descriptionAr: "الباقة الأكثر شعبية مع 10 قراءات مفصلة",
        price: 25.00,
        currency: "USD",
        interval: "monthly",
        stripePriceId: "price_test_basic", // Temporary test price ID
        features: [
          "10 coffee cup readings per month",
          "Detailed symbol analysis",
          "Comprehensive interpretations",
          "24/7 support",
          "Save reading history",
          "Instant notifications"
        ],
        featuresAr: [
          "10 قراءات فنجان قهوة شهرياً",
          "تحليل مفصل للرموز",
          "تفسير شامل للمعاني",
          "دعم على مدار الساعة",
          "حفظ تاريخ القراءات",
          "إشعارات فورية"
        ],
        readingsLimit: 10,
        isActive: true,
      },
      {
        name: "Premium Plan",
        nameAr: "الباقة المتقدمة",
        description: "For professionals with unlimited readings",
        descriptionAr: "للمحترفين مع قراءات غير محدودة",
        price: 35.00,
        currency: "USD",
        interval: "monthly",
        stripePriceId: "price_test_premium", // Temporary test price ID
        features: [
          "Unlimited coffee cup readings",
          "Advanced detailed analysis",
          "Comprehensive interpretations with predictions",
          "Complete reading history",
          "Monthly detailed reports",
          "Direct personal consultations",
          "Priority support"
        ],
        featuresAr: [
          "قراءات فنجان قهوة غير محدودة",
          "تحليل متقدم ومفصل",
          "تفسير شامل مع التوقعات",
          "تاريخ كامل للقراءات",
          "تقارير شهرية مفصلة",
          "استشارات شخصية مباشرة",
          "دعم ذو أولوية"
        ],
        readingsLimit: 999999,
        isActive: true,
      },
    ];

    const createdPlans = [];
    
    for (const planData of testPlans) {
      // Check if plan already exists
      const existing = await prisma.plan.findFirst({
        where: { name: planData.name }
      });
      
      if (!existing) {
        const newPlan = await prisma.plan.create({
          data: planData
        });
        createdPlans.push(newPlan);
      }
    }

    // Get all plans
    const allPlans = await prisma.plan.findMany({
      orderBy: { price: 'asc' }
    });

    return {
      success: true,
      message: `Created ${createdPlans.length} new plans`,
      totalPlans: allPlans.length,
      plans: allPlans.map(p => ({ 
        id: p.id, 
        name: p.name, 
        nameAr: p.nameAr, 
        price: p.price,
        readingsLimit: p.readingsLimit 
      }))
    };

  } catch (error) {
    console.error("Error creating test plans:", error);
    return {
      success: false,
      error: "Failed to create test plans",
      details: error
    };
  }
}
