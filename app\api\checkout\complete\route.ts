import { auth } from "@/auth";
import { NextRequest, NextResponse } from "next/server";
import { stripe } from "@/lib/stripe";
import prisma from "@/lib/prisma";
import { getPlanById } from "@/data/plan";

export async function POST(request: NextRequest) {
  try {
    const session = await auth();

    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { sessionId } = await request.json();

    if (!sessionId) {
      return NextResponse.json(
        { error: "Session ID is required" },
        { status: 400 },
      );
    }

    console.log(
      `Processing checkout completion for session: ${sessionId}, user: ${session.user.id}`,
    );

    // Retrieve the checkout session from Stripe
    const checkoutSession = await stripe.checkout.sessions.retrieve(sessionId, {
      expand: ["subscription", "line_items"],
    });

    if (checkoutSession.payment_status !== "paid") {
      return NextResponse.json(
        { error: "Payment not completed" },
        { status: 400 },
      );
    }

    // Get plan information from metadata
    const planId = checkoutSession.metadata?.planId;
    if (!planId) {
      return NextResponse.json(
        { error: "Plan ID not found in session metadata" },
        { status: 400 },
      );
    }

    // Get the plan details
    const plan = await getPlanById(planId);
    if (!plan) {
      return NextResponse.json({ error: "Plan not found" }, { status: 404 });
    }

    // Get the subscription from Stripe
    const subscriptionObject = checkoutSession.subscription;
    if (!subscriptionObject) {
      return NextResponse.json(
        { error: "Subscription not found" },
        { status: 400 },
      );
    }

    // Retrieve the full subscription object from Stripe to get proper typing
    const subscriptionId =
      typeof subscriptionObject === "string"
        ? subscriptionObject
        : subscriptionObject.id;

    const stripeSubscription = await stripe.subscriptions.retrieve(
      subscriptionId,
    );

    // Check if subscription already exists
    let subscription = await prisma.subscription.findUnique({
      where: { stripeSubscriptionId: stripeSubscription.id },
    });

    if (!subscription) {
      // Create subscription record in database
      subscription = await prisma.subscription.create({
        data: {
          userId: session.user.id,
          stripeSubscriptionId: stripeSubscription.id,
          stripePriceId: plan.stripePriceId,
          status: "ACTIVE",
          currentPeriodStart: new Date(
            (stripeSubscription as any).current_period_start * 1000,
          ),
          currentPeriodEnd: new Date(
            (stripeSubscription as any).current_period_end * 1000,
          ),
          cancelAtPeriodEnd: false,
        },
      });
    } else {
      // Update existing subscription if needed
      subscription = await prisma.subscription.update({
        where: { stripeSubscriptionId: stripeSubscription.id },
        data: {
          status: "ACTIVE",
          currentPeriodStart: new Date(
            (stripeSubscription as any).current_period_start * 1000,
          ),
          currentPeriodEnd: new Date(
            (stripeSubscription as any).current_period_end * 1000,
          ),
          cancelAtPeriodEnd: false,
        },
      });
    }

    // Get current user to check if update is needed
    const currentUser = await prisma.user.findUnique({
      where: { id: session.user.id },
      select: {
        currentPlanId: true,
        readingsLimit: true,
        readingsUsed: true,
        subscriptionStatus: true,
      },
    });

    const isPlanChange = currentUser?.currentPlanId !== plan.id;
    const isSamePlanRenewal = currentUser?.currentPlanId === plan.id;

    // Calculate new readings limit and used count
    let newReadingsLimit = plan.readingsLimit;
    let newReadingsUsed = 0;

    if (isSamePlanRenewal) {
      // For same plan renewal, add the new readings to existing limit
      // Keep current used count, but extend the limit
      newReadingsLimit = (currentUser?.readingsLimit || 0) + plan.readingsLimit;
      newReadingsUsed = currentUser?.readingsUsed || 0;
    } else if (isPlanChange) {
      // For plan change, reset to new plan's limit
      newReadingsLimit = plan.readingsLimit;
      newReadingsUsed = 0;
    }

    // Update user with new plan and readings limit
    const updatedUser = await prisma.user.update({
      where: { id: session.user.id },
      data: {
        currentPlanId: plan.id,
        readingsLimit: newReadingsLimit,
        readingsUsed: newReadingsUsed,
        subscriptionStatus: "ACTIVE",
      },
    });

    console.log(
      `Checkout completed successfully for user ${session.user.id}, plan ${plan.name}, renewal: ${isSamePlanRenewal}`,
    );

    return NextResponse.json({
      success: true,
      message: isSamePlanRenewal
        ? "Subscription renewed successfully"
        : "Subscription activated successfully",
      subscription: {
        id: subscription.id,
        status: subscription.status,
        currentPeriodEnd: subscription.currentPeriodEnd,
        isNew:
          !subscription.createdAt ||
          subscription.createdAt.getTime() > Date.now() - 60000, // Created in last minute
      },
      plan: {
        id: plan.id,
        name: plan.nameAr,
        readingsLimit: plan.readingsLimit,
      },
      user: {
        readingsLimit: updatedUser.readingsLimit,
        readingsUsed: updatedUser.readingsUsed,
        subscriptionStatus: updatedUser.subscriptionStatus,
        planChanged: isPlanChange,
        isRenewal: isSamePlanRenewal,
        addedReadings: isSamePlanRenewal ? plan.readingsLimit : 0,
      },
    });
  } catch (error) {
    console.error("Error completing checkout:", error);
    return NextResponse.json(
      { error: "Failed to complete checkout" },
      { status: 500 },
    );
  }
}
