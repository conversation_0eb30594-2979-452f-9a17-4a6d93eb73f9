import { useMutation, useQueryClient } from "@tanstack/react-query";

interface CheckoutCompletionData {
  sessionId: string;
}

interface CheckoutCompletionResponse {
  success: boolean;
  message: string;
  subscription: {
    id: string;
    status: string;
    currentPeriodEnd: string;
  };
  plan: {
    id: string;
    name: string;
    readingsLimit: number;
  };
  user: {
    readingsLimit: number;
    readingsUsed: number;
    subscriptionStatus: string;
    planChanged: boolean;
    isRenewal: boolean;
    addedReadings: number;
  };
}

async function completeCheckout(
  data: CheckoutCompletionData,
): Promise<CheckoutCompletionResponse> {
  try {
    console.log("Starting checkout completion for session:", data.sessionId);

    const response = await fetch("/api/checkout/complete", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(data),
    });

    console.log("Checkout completion response status:", response.status);

    if (!response.ok) {
      const errorText = await response.text();
      console.error("Checkout completion failed:", errorText);

      let errorMessage = "Failed to complete checkout";
      try {
        const errorJson = JSON.parse(errorText);
        errorMessage = errorJson.error || errorMessage;
      } catch {
        // If not JSON, use the text as error message
        errorMessage = errorText || errorMessage;
      }

      throw new Error(errorMessage);
    }

    const result = await response.json();
    console.log("Checkout completion successful:", result);
    return result;
  } catch (error) {
    console.error("Checkout completion error:", error);

    // Re-throw with a more user-friendly message
    if (error instanceof Error) {
      throw error;
    } else {
      throw new Error("Network error occurred while completing checkout");
    }
  }
}

export function useCheckoutCompletion() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: completeCheckout,
    onSuccess: (data) => {
      // Invalidate user readings to refresh the UI
      queryClient.invalidateQueries({ queryKey: ["userReadings"] });

      // Show success message
      console.log("Checkout completed successfully:", data);
    },
    onError: (error) => {
      console.error("Checkout completion failed:", error);
    },
  });
}
