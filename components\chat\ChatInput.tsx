"use client";
import { useState, useRef } from "react";
import { Button } from "@/components/ui/button";
import { IconSend, IconPhoto, IconX } from "@tabler/icons-react";

interface ChatInputProps {
  onSendMessage: (
    message: string,
    image?: { data: string; type: string },
  ) => void;
  disabled?: boolean;
}

export default function ChatInput({
  onSendMessage,
  disabled = false,
}: ChatInputProps) {
  const [selectedImage, setSelectedImage] = useState<{
    data: string;
    type: string;
    name: string;
  } | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleSendMessage = () => {
    if (!selectedImage || disabled) return;
    onSendMessage("‏اقرأ لي فنجان", selectedImage);
    setSelectedImage(null);
  };

  const handleImageSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // Check if file is an image
    if (!file.type.startsWith("image/")) {
      alert("يرجى اختيار ملف صورة صالح");
      return;
    }

    // Check file size (max 15MB to be safe with OpenAI's 20MB limit)
    if (file.size > 15 * 1024 * 1024) {
      alert("حجم الصورة كبير جداً. يرجى اختيار صورة أصغر من 15 ميجابايت");
      return;
    }

    console.log("Selected image:", {
      name: file.name,
      type: file.type,
      size: `${(file.size / (1024 * 1024)).toFixed(2)} MB`,
    });

    const reader = new FileReader();
    reader.onload = (e) => {
      const result = e.target?.result as string;
      console.log("Image converted to base64, length:", result.length);

      // Validate the base64 format
      if (!result.startsWith("data:image/")) {
        alert("خطأ في تحويل الصورة. يرجى المحاولة مرة أخرى.");
        return;
      }

      setSelectedImage({
        data: result,
        type: file.type,
        name: file.name,
      });
    };
    reader.onerror = (error) => {
      console.error("Error reading file:", error);
      alert("خطأ في قراءة الصورة. يرجى المحاولة مرة أخرى.");
    };
    reader.readAsDataURL(file);
  };

  const removeSelectedImage = () => {
    setSelectedImage(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  };

  return (
    <div>
      {/* Image Preview */}
      {selectedImage && (
        <div className="mb-3 p-2 sm:p-3 bg-gray-50 dark:bg-gray-700 rounded-xl">
          <div className="flex items-center gap-2 sm:gap-3">
            <img
              src={selectedImage.data}
              alt="Selected"
              className="w-12 h-12 sm:w-16 sm:h-16 object-cover rounded-lg"
            />
            <div className="flex-1 min-w-0">
              <p className="text-xs sm:text-sm font-medium text-gray-900 dark:text-white truncate">
                {selectedImage.name}
              </p>
              <p className="text-xs text-gray-500 dark:text-gray-400">
                صورة فنجان القهوة
              </p>
            </div>
            <Button
              onClick={removeSelectedImage}
              size="icon"
              variant="ghost"
              className="text-gray-500 hover:text-red-500 p-1 sm:p-2">
              <IconX className="w-3 h-3 sm:w-4 sm:h-4" />
            </Button>
          </div>
        </div>
      )}

      <div className="flex items-center justify-center gap-4">
        <input
          ref={fileInputRef}
          type="file"
          accept="image/*"
          onChange={handleImageSelect}
          className="hidden"
        />

        {/* Upload Button */}
        <Button
          onClick={() => fileInputRef.current?.click()}
          disabled={disabled}
          className="bg-amber-600 hover:bg-amber-700 text-white px-6 py-3 rounded-xl font-medium text-sm sm:text-base">
          <IconPhoto className="w-5 h-5 sm:w-6 sm:h-6 ml-2" />
          ارفع صورة فنجان القهوة
        </Button>

        {/* Send Button - only show when image is selected */}
        {selectedImage && (
          <Button
            onClick={handleSendMessage}
            disabled={!selectedImage || disabled}
            className="bg-green-600 hover:bg-green-700 text-white px-6 py-3 rounded-xl font-medium text-sm sm:text-base">
            <IconSend className="w-5 h-5 sm:w-6 sm:h-6 ml-2" />
            إرسال
          </Button>
        )}
      </div>
    </div>
  );
}
