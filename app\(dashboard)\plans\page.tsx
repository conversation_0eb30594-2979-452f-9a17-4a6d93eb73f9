"use client";

import Image from "next/image";
import { Check, Loader2, <PERSON>, Zap, Crown } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { useQuery } from "@tanstack/react-query";
import { usePurchasePlan } from "@/hooks/use-purchase-plan";
import { useUserReadings } from "@/hooks/use-user-readings";

interface Plan {
  id: string;
  name: string;
  nameAr: string;
  description: string;
  descriptionAr: string;
  price: number;
  currency: string;
  interval: string;
  stripePriceId: string;
  features: string[];
  featuresAr: string[];
  readingsLimit: number;
  isActive: boolean;
}

async function fetchPlans(): Promise<Plan[]> {
  const response = await fetch("/api/plans");
  if (!response.ok) {
    throw new Error("Failed to fetch plans");
  }
  return response.json();
}

export default function ModernPlansPage() {
  const {
    data: plans,
    isLoading,
    error,
  } = useQuery({
    queryKey: ["plans"],
    queryFn: fetchPlans,
  });

  const { data: userReadings } = useUserReadings();
  const purchasePlanMutation = usePurchasePlan();

  const handlePurchase = (planId: string) => {
    purchasePlanMutation.mutate(planId);
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="h-12 w-12 animate-spin text-amber-500 mx-auto mb-4" />
          <p className="text-xl text-gray-600 dark:text-gray-400">
            جاري تحميل الباقات...
          </p>
        </div>
      </div>
    );
  }

  if (error || !plans || plans.length === 0) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 flex items-center justify-center">
        <div className="text-center">
          <p className="text-xl text-red-500">خطأ في تحميل الباقات</p>
        </div>
      </div>
    );
  }

  const getPlanIcon = (planName: string) => {
    if (planName.includes("Premium")) return <Crown className="w-6 h-6" />;
    if (planName.includes("Basic")) return <Star className="w-6 h-6" />;
    if (planName.includes("Starter")) return <Zap className="w-6 h-6" />;
    return <Check className="w-6 h-6" />;
  };

  return (
    <div className="w-full bg-gradient-to-br from-gray-50 via-white to-gray-100 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900 overflow-y-auto">
      {/* Hero Section */}
      <div className="relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-r from-amber-500/10 via-orange-500/10 to-red-500/10"></div>
        <div className="relative max-w-7xl mx-auto px-4 py-12 md:py-16 text-center">
          <div className="mb-6 md:mb-8">
            <h1 className="text-3xl md:text-4xl lg:text-5xl font-bold mb-4 bg-gradient-to-r from-amber-600 via-orange-600 to-red-600 bg-clip-text text-transparent">
              اختر باقتك المثالية
            </h1>
            <p className="text-base md:text-lg lg:text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto leading-relaxed px-4">
              اكتشف أسرار المستقبل من خلال قراءة الفنجان التقليدية مع باقاتنا
              المتنوعة
            </p>
          </div>

          {/* Current Plan Status */}
          {userReadings?.currentPlan && (
            <div className="flex flex-col sm:flex-row items-center gap-3 sm:gap-4 bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-xl px-4 sm:px-6 py-3 shadow-lg border border-gray-200 dark:border-gray-700 mx-4">
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                <span className="font-medium text-sm sm:text-base text-gray-800 dark:text-gray-200">
                  الباقة الحالية: {userReadings.currentPlan.nameAr}
                </span>
              </div>
              <div className="hidden sm:block h-4 w-px bg-gray-300 dark:bg-gray-600"></div>
              <div className="flex items-center gap-2">
                <Image
                  src="/coffeecup.png"
                  alt="Coffee"
                  width={20}
                  height={20}
                />
                <span className="font-medium text-sm sm:text-base text-amber-600 dark:text-amber-400">
                  {userReadings.remainingReadings} قراءة متبقية
                </span>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Plans Grid */}
      <div className="max-w-7xl mx-auto px-4 pb-12 md:pb-16">
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 md:gap-6">
          {plans.map((plan) => {
            const isPopular =
              plan.name === "Basic Plan" || plan.nameAr === "الباقة الأساسية";
            const isFree = plan.price === 0;
            const isCurrentPlan = userReadings?.currentPlan?.id === plan.id;
            const isPremium =
              plan.name === "Premium Plan" || plan.nameAr === "الباقة المتقدمة";

            return (
              <div
                key={plan.id}
                className={`group relative bg-white dark:bg-gray-800 rounded-2xl p-4 md:p-6 transition-all duration-500 hover:scale-105 ${
                  isPopular
                    ? "ring-4 ring-amber-400 shadow-2xl shadow-amber-500/25 transform scale-105"
                    : isPremium
                    ? "ring-4 ring-purple-400 shadow-2xl shadow-purple-500/25"
                    : "shadow-xl hover:shadow-2xl border border-gray-200 dark:border-gray-700"
                } ${
                  isCurrentPlan
                    ? "bg-gradient-to-br from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20"
                    : ""
                }`}
                dir="rtl">
                {/* Popular Badge */}
                {isPopular && (
                  <div className="absolute -top-4 left-1/2 transform -translate-x-1/2 z-10">
                    <div className="bg-gradient-to-r from-amber-500 to-orange-500 text-white px-6 py-2 rounded-full font-bold text-sm shadow-lg flex items-center gap-2">
                      <Star className="w-4 h-4 fill-current" />
                      الأكثر اختياراً
                    </div>
                  </div>
                )}

                {/* Premium Badge */}
                {isPremium && !isPopular && (
                  <div className="absolute -top-4 left-1/2 transform -translate-x-1/2 z-10">
                    <div className="bg-gradient-to-r from-purple-500 to-indigo-500 text-white px-6 py-2 rounded-full font-bold text-sm shadow-lg flex items-center gap-2">
                      <Crown className="w-4 h-4 fill-current" />
                      للمحترفين
                    </div>
                  </div>
                )}

                {/* Current Plan Badge */}
                {isCurrentPlan && (
                  <div className="absolute -top-4 right-4 z-10">
                    <div className="bg-gradient-to-r from-green-500 to-emerald-500 text-white px-4 py-2 rounded-full text-xs font-bold shadow-lg flex items-center gap-1">
                      <Check className="w-3 h-3" />
                      الباقة الحالية
                    </div>
                  </div>
                )}

                {/* Plan Icon & Header */}
                <div className="text-center mb-6">
                  <div
                    className={`inline-flex items-center justify-center w-12 h-12 md:w-14 md:h-14 rounded-xl mb-3 ${
                      isPremium
                        ? "bg-gradient-to-r from-purple-500 to-indigo-500"
                        : isPopular
                        ? "bg-gradient-to-r from-amber-500 to-orange-500"
                        : isFree
                        ? "bg-gradient-to-r from-green-500 to-emerald-500"
                        : "bg-gradient-to-r from-gray-600 to-gray-700"
                    } text-white shadow-lg`}>
                    <div className="w-5 h-5 md:w-6 md:h-6">
                      {getPlanIcon(plan.name)}
                    </div>
                  </div>

                  <h3 className="text-lg md:text-xl font-bold text-gray-900 dark:text-white mb-2">
                    {plan.nameAr}
                  </h3>
                  <p className="text-sm md:text-base text-gray-600 dark:text-gray-400 leading-relaxed">
                    {plan.descriptionAr}
                  </p>
                </div>

                {/* Price Section */}
                <div className="text-center mb-6">
                  <div className="flex items-center justify-center gap-1 mb-2">
                    {isFree ? (
                      <span className="text-2xl md:text-3xl font-bold bg-gradient-to-r from-green-600 to-emerald-600 bg-clip-text text-transparent">
                        مجاني
                      </span>
                    ) : (
                      <>
                        <span
                          className={`text-3xl md:text-4xl font-bold bg-gradient-to-r ${
                            isPremium
                              ? "from-purple-600 to-indigo-600"
                              : isPopular
                              ? "from-amber-600 to-orange-600"
                              : "from-gray-700 to-gray-800"
                          } bg-clip-text text-transparent`}>
                          ${plan.price}
                        </span>
                        <span className="text-gray-500 dark:text-gray-400 text-sm md:text-base">
                          /
                          {plan.interval === "monthly"
                            ? "شهر"
                            : plan.interval === "yearly"
                            ? "سنة"
                            : "مرة"}
                        </span>
                      </>
                    )}
                  </div>
                  <div
                    className={`inline-flex items-center gap-2 px-4 py-2 rounded-full text-sm font-medium ${
                      isPremium
                        ? "bg-purple-50 dark:bg-purple-900/20 text-purple-700 dark:text-purple-300"
                        : isPopular
                        ? "bg-amber-50 dark:bg-amber-900/20 text-amber-700 dark:text-amber-300"
                        : isFree
                        ? "bg-green-50 dark:bg-green-900/20 text-green-700 dark:text-green-300"
                        : "bg-gray-50 dark:bg-gray-900/20 text-gray-700 dark:text-gray-300"
                    }`}>
                    <Image
                      src="/coffeecup.png"
                      alt="Coffee"
                      width={16}
                      height={16}
                    />
                    {plan.readingsLimit === 999999
                      ? "قراءات غير محدودة"
                      : `${plan.readingsLimit} قراءة`}
                  </div>
                </div>

                {/* Features */}
                <div className="space-y-3 mb-6">
                  {plan.featuresAr.map((feature, index) => (
                    <div
                      key={index}
                      className="flex items-start gap-3"
                      dir="rtl">
                      <div
                        className={`flex-shrink-0 w-6 h-6 rounded-full flex items-center justify-center mt-0.5 ${
                          isPremium
                            ? "bg-gradient-to-r from-purple-500 to-indigo-500"
                            : isPopular
                            ? "bg-gradient-to-r from-amber-500 to-orange-500"
                            : "bg-gradient-to-r from-green-500 to-emerald-500"
                        }`}>
                        <Check className="w-2.5 h-2.5 text-white" />
                      </div>
                      <span className="text-sm md:text-base text-gray-700 dark:text-gray-300 leading-relaxed">
                        {feature}
                      </span>
                    </div>
                  ))}
                </div>

                {/* Renewal Notice */}
                {isCurrentPlan && (
                  <div className="mb-4 p-3 bg-green-50 dark:bg-green-900/20 rounded-xl border border-green-200 dark:border-green-800">
                    <div className="flex items-center gap-2 text-green-700 dark:text-green-300 text-xs md:text-sm">
                      <div className="w-1.5 h-1.5 bg-green-500 rounded-full animate-pulse"></div>
                      يمكنك تجديد هذه الباقة لإضافة المزيد من القراءات
                    </div>
                  </div>
                )}

                {/* Action Button */}
                <Button
                  onClick={() => handlePurchase(plan.id)}
                  disabled={purchasePlanMutation.isPending || isFree}
                  className={`w-full py-3 md:py-4 rounded-xl font-bold text-base md:text-lg transition-all duration-300 transform hover:scale-105 ${
                    isFree
                      ? "bg-gray-100 text-gray-500 cursor-not-allowed"
                      : isPremium
                      ? "bg-gradient-to-r from-purple-500 to-indigo-500 hover:from-purple-600 hover:to-indigo-600 text-white shadow-lg hover:shadow-xl"
                      : isPopular
                      ? "bg-gradient-to-r from-amber-500 to-orange-500 hover:from-amber-600 hover:to-orange-600 text-white shadow-lg hover:shadow-xl"
                      : isCurrentPlan
                      ? "bg-gradient-to-r from-green-500 to-emerald-500 hover:from-green-600 hover:to-emerald-600 text-white shadow-lg hover:shadow-xl"
                      : "bg-gradient-to-r from-gray-700 to-gray-800 hover:from-gray-600 hover:to-gray-700 text-white shadow-lg hover:shadow-xl"
                  }`}>
                  {purchasePlanMutation.isPending ? (
                    <div className="flex items-center justify-center gap-2">
                      <Loader2 className="h-5 w-5 animate-spin" />
                      جاري المعالجة...
                    </div>
                  ) : isFree ? (
                    "مجاني"
                  ) : isCurrentPlan ? (
                    <div className="flex items-center justify-center gap-2">
                      <span>🔄</span>
                      تجديد الاشتراك
                    </div>
                  ) : (
                    <div className="flex items-center justify-center gap-2">
                      <span>✨</span>
                      اشترك الآن
                    </div>
                  )}
                </Button>
              </div>
            );
          })}
        </div>

        {/* Trust Indicators */}
        <div className="mt-12 md:mt-16 text-center px-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-4xl mx-auto">
            <div className="flex items-center justify-center gap-3 text-gray-600 dark:text-gray-400">
              <div className="w-10 h-10 md:w-12 md:h-12 bg-green-100 dark:bg-green-900/20 rounded-full flex items-center justify-center">
                <Check className="w-5 h-5 md:w-6 md:h-6 text-green-600" />
              </div>
              <div className="text-right">
                <div className="font-semibold text-sm md:text-base">
                  ضمان الجودة
                </div>
                <div className="text-xs md:text-sm">قراءات دقيقة ومفصلة</div>
              </div>
            </div>
            <div className="flex items-center justify-center gap-3 text-gray-600 dark:text-gray-400">
              <div className="w-10 h-10 md:w-12 md:h-12 bg-blue-100 dark:bg-blue-900/20 rounded-full flex items-center justify-center">
                <Zap className="w-5 h-5 md:w-6 md:h-6 text-blue-600" />
              </div>
              <div className="text-right">
                <div className="font-semibold text-sm md:text-base">
                  نتائج فورية
                </div>
                <div className="text-xs md:text-sm">
                  احصل على قراءتك في ثوانٍ
                </div>
              </div>
            </div>
            <div className="flex items-center justify-center gap-3 text-gray-600 dark:text-gray-400">
              <div className="w-10 h-10 md:w-12 md:h-12 bg-purple-100 dark:bg-purple-900/20 rounded-full flex items-center justify-center">
                <Crown className="w-5 h-5 md:w-6 md:h-6 text-purple-600" />
              </div>
              <div className="text-right">
                <div className="font-semibold text-sm md:text-base">
                  خبرة عريقة
                </div>
                <div className="text-xs md:text-sm">
                  تقاليد أصيلة في قراءة الفنجان
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
