import Image from "next/image";

interface WelcomeScreenProps {
  onPromptSelect: (prompt: string) => void;
}

export default function WelcomeScreen({ onPromptSelect }: WelcomeScreenProps) {
  return (
    <div className="text-center py-8 sm:py-12">
      {/* Persona GIF */}
      <div className="flex justify-center mb-6 sm:mb-8">
        <Image
          src="/morjana.gif"
          alt="morjana"
          width={200}
          height={200}
          className="w-32 h-32 sm:w-48 sm:h-48 lg:w-56 lg:h-56 object-contain rounded-lg"
          unoptimized
        />
      </div>

      <h1 className="text-xl sm:text-2xl lg:text-3xl font-bold text-gray-900 dark:text-white mb-2 sm:mb-4">
        مرحباً بك في فنجاني
      </h1>
      <p className="text-sm sm:text-base lg:text-lg text-gray-600 dark:text-gray-400 mb-6 sm:mb-8 px-4">
        كيف يمكنني مساعدتك اليوم؟
      </p>
    </div>
  );
}
