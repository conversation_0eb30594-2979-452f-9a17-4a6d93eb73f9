export interface ChatHistoryItem {
  id: string;
  title: string;
  date: string;
  userMessage: string;
  aiResponse: string;
  imageData?: string;
  imageType?: string;
  createdAt: Date;
}

export interface DashboardUser {
  id: string;
  name: string;
  email: string;
  image?: string;
}

export interface Message {
  id: number;
  content: string;
  sender: "user" | "assistant";
  timestamp: Date;
  image?: string; // Base64 encoded image or image URL
  imageType?: string; // MIME type of the image
}
