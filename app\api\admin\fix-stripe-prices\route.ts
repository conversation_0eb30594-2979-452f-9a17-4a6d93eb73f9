import { NextResponse } from "next/server";
import { stripe } from "@/lib/stripe";
import prisma from "@/lib/prisma";

export async function POST() {
  try {
    console.log("Fixing Stripe price IDs...");

    // First, let's see what plans we have in the database
    const plans = await prisma.plan.findMany({
      where: { price: { gt: 0 } }, // Only paid plans
      orderBy: { price: "asc" },
    });

    console.log(
      "Found plans in database:",
      plans.map((p) => ({
        name: p.name,
        price: p.price,
        currentPriceId: p.stripePriceId,
      })),
    );

    const results = [];

    for (const plan of plans) {
      try {
        // Check if the current price ID exists in Stripe
        let priceExists = false;
        try {
          await stripe.prices.retrieve(plan.stripePriceId);
          priceExists = true;
          console.log(`Price ${plan.stripePriceId} exists for ${plan.name}`);
        } catch (error) {
          console.log(
            `Price ${plan.stripePriceId} does not exist for ${plan.name}, creating new one...`,
          );
        }

        if (!priceExists) {
          // Create a new product
          const product = await stripe.products.create({
            name: plan.name,
            description: plan.description,
            metadata: {
              planId: plan.id,
              nameAr: plan.nameAr,
            },
          });

          // Create a new price
          const price = await stripe.prices.create({
            product: product.id,
            unit_amount: Math.round(plan.price * 100), // Convert to cents
            currency: "usd",
            recurring: {
              interval: "month",
            },
            metadata: {
              planId: plan.id,
              planName: plan.name,
            },
          });

          // Update the plan in database with new price ID
          await prisma.plan.update({
            where: { id: plan.id },
            data: { stripePriceId: price.id },
          });

          results.push({
            planName: plan.name,
            oldPriceId: plan.stripePriceId,
            newPriceId: price.id,
            productId: product.id,
            status: "created",
          });

          console.log(`Created new price ${price.id} for ${plan.name}`);
        } else {
          results.push({
            planName: plan.name,
            priceId: plan.stripePriceId,
            status: "already_exists",
          });
        }
      } catch (error) {
        console.error(`Error processing plan ${plan.name}:`, error);
        results.push({
          planName: plan.name,
          error: error instanceof Error ? error.message : String(error),
          status: "error",
        });
      }
    }

    // Get updated plans from database
    const updatedPlans = await prisma.plan.findMany({
      orderBy: { price: "asc" },
    });

    return NextResponse.json({
      success: true,
      message: "Stripe price IDs fixed",
      results,
      updatedPlans: updatedPlans.map((p) => ({
        id: p.id,
        name: p.name,
        nameAr: p.nameAr,
        price: p.price,
        stripePriceId: p.stripePriceId,
        readingsLimit: p.readingsLimit,
      })),
    });
  } catch (error) {
    console.error("Error fixing Stripe prices:", error);
    return NextResponse.json(
      {
        error: "Failed to fix Stripe prices",
        details: error instanceof Error ? error.message : String(error),
      },
      { status: 500 },
    );
  }
}
