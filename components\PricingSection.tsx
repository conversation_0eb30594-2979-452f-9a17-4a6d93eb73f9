"use client";

import Image from "next/image";
import { ArrowLeft } from "lucide-react";

export default function PricingSection() {
  const pricingData = [
    {
      id: 1,
      name: "باقة  البداية",
      price: "3 دولار",
      features: [
        "• قراءة واحدة",
        "• تحليل أساسي",
        "• نتائج فورية",
        "• دعم أساسي",
      ],
    },
    {
      id: 2,
      name: "الباقة الأساسية",
      price: "25 دولار",
      features: [
        "• 10 قراءة",
        "• تحليل مفصل",
        "• تفسير شامل",
        "• دعم متقدم",
        "• حفظ النتائج",
        "• إشعارات فورية",
      ],
    },
    {
      id: 3,
      name: "الباقة المتقدمة",
      price: "35 دولار",
      features: [
        "• 15 قراءة",
        "• تحليل متقدم",
        "• تفسير مفصل",
        "• حفظ التاريخ",
        "• تقارير شهرية",
        "• استشارات خاصة",
        "• أولوية في الدعم",
      ],
    },
  ];

  return (
    <section id="offers" className="relative py-20 overflow-hidden">
      {/* Background Shape */}
      <div className="absolute flex items-center justify-center inset-0 -top-1/2 z-5">
        <Image
          src="/shape.png"
          alt="Background Shape"
          width={1758}
          height={750}
          className="w-[1500px] h-[750px] object-contain object-center opacity-40"
        />
      </div>

      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header Text */}
        <div className="text-center mb-16">
          <p className="text-lg text-gray-700 mb-4 leading-relaxed max-w-4xl mx-auto">
            قراءة الفنجان دي مش موضة جديدة، دي عادة قديمة قوي، اتعلمناها من
            جداتنا، وجابتنا معاها من زمان الشام والأناضول، وناس تقول من تركيا،
            وناس تقول من زمان الفراعنة، الله أعلم
          </p>
          <p className="text-lg text-gray-700 mb-4 leading-relaxed max-w-4xl mx-auto">
            أنا يا بنتي بقالي سنين بقرأ الفنجان، وشفت فيه أفراح، وزعل، وسفر،
            وعيون بتحب وقلوب بتوجع، وناس رايحة وناس راجعة. ومهما كانت الدنيا،
            الفنجان دايماً فيه كلمة تريح القلب، أو تحذّر منه، على حسب اللي
            مكتوبلك.
          </p>
          <p className="text-lg text-gray-700 mb-8 leading-relaxed max-w-4xl mx-auto">
            فإنتي دلوقتي هاتي فنجانك، واشربي منه وآهي ساعة راحة، ونقعد نحكي
            ونقرا ونضحك، يمكن نلاقي كلمة تفرّح قلبك، أو علامة تدلّك على اللي
            جايلك
          </p>
        </div>

        {/* Call to Action Button */}
        <div className="flex justify-center mb-12">
          <div className="relative">
            <button className="flex items-center justify-center bg-[#E5B565] text-black px-10 py-4 rounded-full font-bold text-lg shadow-lg hover:bg-[#D4A554] transition-colors">
              <span className="text-center px-5">إبدأ القراءة المجانية</span>
              <ArrowLeft className="w-6 h-6 ml-3" />
            </button>
            <Image
              src="/Coffeeicon.svg"
              alt="Coffee Icon"
              width={50}
              height={50}
              className="absolute -top-2 -right-2 z-10"
            />
          </div>
        </div>

        {/* Available Offers Title */}
        <div className="text-center mb-12">
          <h2 className="text-[35px] font-bold bg-[#E5B565] px-8 py-3 rounded-full inline-block">
            العروض المتوفرة
          </h2>
        </div>

        {/* Pricing Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-6xl mx-auto">
          {pricingData.map((pkg) => (
            <div
              key={pkg.id}
              className="bg-transparent rounded-3xl border-2 border-black p-6 pt-12 text-center relative min-h-[600px] flex flex-col"
              dir="rtl">
              {/* Package Name */}
              <div className="absolute -top-7 left-1/2 transform -translate-x-1/2 bg-[#E5B565] text-black px-6 py-3 rounded-full font-bold text-lg z-10">
                {pkg.name}
              </div>

              {/* Price Badge */}
              <div
                className="text-white px-4 py-2 rounded-lg text-sm font-bold mb-6 inline-block mx-auto"
                style={{ backgroundColor: "#412B4E" }}>
                {pkg.price}
              </div>

              {/* Features List */}
              <ul className="text-right space-y-4 mb-8 flex-grow" dir="rtl">
                {pkg.features.map((feature, index) => (
                  <li key={index} className="flex items-center text-lg">
                    <span>{feature}</span>
                  </li>
                ))}
              </ul>

              {/* Coffee Cup */}
              <div className="mb-6">
                <div className="flex justify-center items-center mb-4">
                  <Image
                    src="/coffeecup.png"
                    alt="Coffee Cup"
                    width={128}
                    height={128}
                    className="object-contain"
                  />
                </div>
              </div>

              {/* Button */}
              <button className="w-full bg-[#E5B565] text-black py-3 rounded-full font-bold hover:bg-[#D4A554] transition-colors">
                إختر هذا العرض
              </button>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}
