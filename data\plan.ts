import prisma from "@/lib/prisma";

export const getFreePlan = async () => {
  try {
    const freePlan = await prisma.plan.findFirst({
      where: {
        price: 0,
        isActive: true,
      },
    });
    return freePlan;
  } catch (error) {
    console.log("🚀 ~ getFreePlan ~ error:", error);
    return null;
  }
};

export const getPlanById = async (id: string) => {
  try {
    const plan = await prisma.plan.findUnique({
      where: {
        id,
      },
    });
    return plan;
  } catch (error) {
    console.log("🚀 ~ getPlanById ~ error:", error);
    return null;
  }
};

export const getAllActivePlans = async () => {
  try {
    const plans = await prisma.plan.findMany({
      where: {
        isActive: true,
      },
      orderBy: {
        price: "asc",
      },
    });
    return plans;
  } catch (error) {
    console.log("🚀 ~ getAllActivePlans ~ error:", error);
    return [];
  }
};
