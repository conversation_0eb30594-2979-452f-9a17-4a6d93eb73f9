"use client";
import { useState, useRef, useEffect } from "react";
import { useSearchParams } from "next/navigation";
import MessageBubble from "@/components/chat/MessageBubble";
import TypingIndicator from "@/components/chat/TypingIndicator";
import ChatInput from "@/components/chat/ChatInput";
import WelcomeScreen from "@/components/chat/WelcomeScreen";
import { Message } from "@/types/dashboard";
import { useSendMessage, useUserReadings } from "@/hooks/use-user-readings";
import { useCheckoutCompletion } from "@/hooks/use-checkout-completion";
import { useConversation } from "@/contexts/ConversationContext";

export default function ChatPage() {
  const [messages, setMessages] = useState<Message[]>([
    {
      id: 1,
      content:
        "مرحباً! أنا هنا لمساعدتك في قراءة الفنجان وتفسير الأحلام. كيف يمكنني مساعدتك اليوم؟",
      sender: "assistant",
      timestamp: new Date(),
    },
  ]);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const searchParams = useSearchParams();

  const { data: userReadings } = useUserReadings();
  const sendMessageMutation = useSendMessage();
  const checkoutCompletionMutation = useCheckoutCompletion();
  const {
    selectedConversation,
    setSelectedConversation,
    loadConversationMessages,
    isNewChat,
    setIsNewChat,
  } = useConversation();

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  // Load selected conversation when it changes
  useEffect(() => {
    if (selectedConversation) {
      const conversationMessages =
        loadConversationMessages(selectedConversation);
      setMessages(conversationMessages);
      // Clear the selected conversation after loading
      setSelectedConversation(null);
    }
  }, [selectedConversation, loadConversationMessages, setSelectedConversation]);

  // Handle new chat creation
  useEffect(() => {
    if (isNewChat) {
      // Reset to welcome message for new chat
      setMessages([
        {
          id: 1,
          content:
            "مرحباً! أنا هنا لمساعدتك في قراءة الفنجان وتفسير الأحلام. كيف يمكنني مساعدتك اليوم؟",
          sender: "assistant",
          timestamp: new Date(),
        },
      ]);
      // Clear the new chat flag
      setIsNewChat(false);
    }
  }, [isNewChat, setIsNewChat]);

  // Handle successful payment redirect
  useEffect(() => {
    const success = searchParams.get("success");
    const sessionId = searchParams.get("session_id");

    if (
      success === "true" &&
      sessionId &&
      !checkoutCompletionMutation.isPending
    ) {
      // Prevent multiple calls by checking if already processing
      const hasProcessedKey = `processed_${sessionId}`;
      if (sessionStorage.getItem(hasProcessedKey)) {
        console.log("Session already processed, skipping...");
        return;
      }

      // Mark as processing
      sessionStorage.setItem(hasProcessedKey, "true");

      console.log("Processing checkout completion for session:", sessionId);

      // Add processing message to chat
      const processingMessage: Message = {
        id: Date.now(),
        content: "⏳ جاري تفعيل اشتراكك... يرجى الانتظار قليلاً.",
        sender: "assistant",
        timestamp: new Date(),
      };
      setMessages((prev) => [...prev, processingMessage]);

      // Complete the checkout process
      checkoutCompletionMutation.mutate(
        { sessionId },
        {
          onSuccess: (data) => {
            console.log("Checkout completion successful:", data);

            // Remove processing message and add success message
            setMessages((prev) => {
              const filtered = prev.filter(
                (msg) => msg.id !== processingMessage.id,
              );

              let successContent = "";
              if (data.user.isRenewal) {
                successContent = `🎉 تم تجديد اشتراكك بنجاح!\n\nالباقة: ${data.plan.name}\nتم إضافة: ${data.user.addedReadings} قراءة جديدة\nإجمالي القراءات المتاحة: ${data.user.readingsLimit}\nالقراءات المستخدمة: ${data.user.readingsUsed}\n\nشكراً لك على تجديد اشتراكك!`;
              } else {
                successContent = `🎉 تم تفعيل اشتراكك بنجاح!\n\nالباقة: ${data.plan.name}\nعدد القراءات: ${data.user.readingsLimit}\n\nيمكنك الآن الاستمتاع بخدماتنا المتقدمة!`;
              }

              const successMessage: Message = {
                id: Date.now(),
                content: successContent,
                sender: "assistant",
                timestamp: new Date(),
              };
              return [...filtered, successMessage];
            });

            // Clear URL parameters after a short delay
            setTimeout(() => {
              window.history.replaceState({}, "", "/chat");
            }, 1000);
          },
          onError: (error) => {
            console.error("Checkout completion failed:", error);

            // Remove the processing flag on error so user can retry
            sessionStorage.removeItem(hasProcessedKey);

            // Remove processing message and add error message
            setMessages((prev) => {
              const filtered = prev.filter(
                (msg) => msg.id !== processingMessage.id,
              );
              const errorMessage: Message = {
                id: Date.now(),
                content: `❌ حدث خطأ في تفعيل الاشتراك: ${error.message}\n\nيرجى إعادة تحميل الصفحة للمحاولة مرة أخرى.`,
                sender: "assistant",
                timestamp: new Date(),
              };
              return [...filtered, errorMessage];
            });
          },
        },
      );
    }
  }, [searchParams, checkoutCompletionMutation]);

  const handleSendMessage = async (
    content: string,
    image?: { data: string; type: string },
  ) => {
    // Check if user has remaining readings
    if (userReadings && userReadings.remainingReadings <= 0) {
      const errorMessage: Message = {
        id: Date.now(),
        content: "لقد استنفدت عدد القراءات المتاحة. يرجى ترقية باقتك للمتابعة.",
        sender: "assistant",
        timestamp: new Date(),
      };
      setMessages((prev) => [...prev, errorMessage]);
      return;
    }

    const userMessage: Message = {
      id: Date.now(),
      content,
      sender: "user",
      timestamp: new Date(),
      image: image?.data,
      imageType: image?.type,
    };

    setMessages((prev) => [...prev, userMessage]);

    // Send message using mutation
    sendMessageMutation.mutate(
      { content, image },
      {
        onSuccess: (data) => {
          const assistantMessage: Message = {
            id: Date.now() + 1,
            content: data.response,
            sender: "assistant",
            timestamp: new Date(),
          };
          setMessages((prev) => [...prev, assistantMessage]);
        },
        onError: (error) => {
          const errorMessage: Message = {
            id: Date.now() + 1,
            content:
              error.message ||
              "حدث خطأ أثناء إرسال الرسالة. يرجى المحاولة مرة أخرى.",
            sender: "assistant",
            timestamp: new Date(),
          };
          setMessages((prev) => [...prev, errorMessage]);
        },
      },
    );
  };

  return (
    <div className="flex flex-col h-full bg-white dark:bg-gray-800">
      {/* Messages Area */}
      <div className="flex-1 overflow-y-auto px-3 sm:px-4 lg:px-6 py-4 sm:py-6">
        <div className="max-w-4xl mx-auto space-y-4 sm:space-y-6">
          {messages.length === 1 && (
            <WelcomeScreen onPromptSelect={handleSendMessage} />
          )}

          {messages.map((message) => (
            <MessageBubble key={message.id} message={message} />
          ))}

          {sendMessageMutation.isPending && <TypingIndicator />}

          <div ref={messagesEndRef} />
        </div>
      </div>

      {/* Input Area */}
      <div className="border-t border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800">
        <div className="p-3 sm:p-4 lg:p-6">
          <div className="max-w-4xl mx-auto">
            <ChatInput
              onSendMessage={handleSendMessage}
              disabled={
                sendMessageMutation.isPending ||
                userReadings?.remainingReadings === 0
              }
            />
          </div>
        </div>
      </div>
    </div>
  );
}
