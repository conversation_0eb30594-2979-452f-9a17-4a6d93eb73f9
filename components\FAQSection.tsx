"use client";
import Image from "next/image";
import { useState } from "react";

export default function FAQSection() {
  const [openAccordion, setOpenAccordion] = useState<number | null>(null);

  const faqs = [
    {
      id: 1,
      question: "كيف تعمل قراءة الفنجان؟",
      answer:
        "قراءة الفنجان هي فن تقليدي يعتمد على تفسير أشكال وأنماط بقايا القهوة في الفنجان للكشف عن معلومات حول المستقبل والحاضر.",
    },
    {
      id: 2,
      question: "هل النتائج دقيقة؟",
      answer:
        "تعتمد دقة النتائج على خبرة القارئ وجودة الصور المرسلة. نحن نستخدم تقنيات متقدمة لتحليل الأنماط وتقديم قراءات مفصلة.",
    },
    {
      id: 3,
      question: "كم من الوقت يستغرق التحليل؟",
      answer:
        "التحليل فوري! بمجرد رفع صورة الفنجان، ستحصل على النتائج في ثوانٍ معدودة. لا تحتاج للانتظار ساعات أو أيام.",
    },
    {
      id: 4,
      question: "هل بياناتي آمنة؟",
      answer:
        "نعم، نحن نأخذ خصوصيتك على محمل الجد. جميع البيانات مشفرة ومحمية، ولا نشارك معلوماتك مع أي طرف ثالث. يمكنك حذف بياناتك في أي وقت.",
    },
    {
      id: 5,
      question: "هل يمكنني إلغاء الاشتراك في أي وقت؟",
      answer:
        "نعم، يمكنك إلغاء اشتراكك في أي وقت من خلال لوحة التحكم. لا توجد رسوم إضافية أو التزامات طويلة المدى.",
    },
    {
      id: 6,
      question: "ما هي أنواع القراءات المتاحة؟",
      answer:
        "نقدم قراءات شاملة تغطي جميع جوانب الحياة: الحب والعلاقات، العمل والمال، الصحة والعائلة، والسفر والمستقبل. كل قراءة مفصلة ومخصصة.",
    },
    {
      id: 7,
      question: "هل يمكنني الحصول على قراءة مجانية؟",
      answer:
        "نعم! نقدم قراءة مجانية واحدة لكل مستخدم جديد. يمكنك تجربة الخدمة كاملة قبل الاشتراك في أي خطة مدفوعة.",
    },
    {
      id: 8,
      question: "كيف يمكنني الحصول على الدعم؟",
      answer:
        "نحن نقدم دعم 24/7 من خلال الدردشة المباشرة والبريد الإلكتروني. فريق الدعم العربي متخصص ومستعد لمساعدتك في أي وقت.",
    },
  ];

  const toggleAccordion = (id: number) => {
    setOpenAccordion(openAccordion === id ? null : id);
  };

  return (
    <section id="faq" className="relative py-20 px-4 overflow-hidden">
      {/* Background Image */}
      <div className="absolute inset-0 z-0">
        <Image
          src="/qn.png"
          alt="FAQ Background"
          fill
          className="object-cover object-center"
        />
        {/* Overlay for better text readability */}
        {/* <div className="absolute inset-0 bg-black/20"></div> */}
      </div>

      <div className="relative z-10 max-w-7xl mx-auto">
        {/* FAQ Content */}
        <div className="flex flex-col lg:flex-row items-start justify-between gap-12">
          {/* Right side - FAQ Accordion */}
          <div className="lg:w-1/2 space-y-4">
            {faqs.map((faq) => (
              <div
                key={faq.id}
                className="bg-white/90 rounded-lg border border-[#E5B565] overflow-hidden">
                {/* Accordion Header */}
                <button
                  onClick={() => toggleAccordion(faq.id)}
                  className="w-full px-6 py-4 text-right flex items-center justify-between hover:bg-[#E5B565]/10 transition-colors">
                  {/* Question Text */}
                  <h3 className="text-[#E5B565] text-lg font-bold leading-relaxed">
                    {faq.question}
                  </h3>

                  {/* Arrow Icon */}
                  <div
                    className={`transform transition-transform duration-200 ${
                      openAccordion === faq.id ? "rotate-180" : ""
                    }`}>
                    <svg
                      className="w-6 h-6 text-[#E5B565]"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24">
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M19 9l-7 7-7-7"
                      />
                    </svg>
                  </div>
                </button>

                {/* Accordion Content */}
                <div
                  className={`overflow-hidden transition-all duration-300 ${
                    openAccordion === faq.id
                      ? "max-h-96 opacity-100"
                      : "max-h-0 opacity-0"
                  }`}>
                  <div className="px-6 pb-4">
                    <p className="text-gray-700 text-sm leading-relaxed">
                      {faq.answer}
                    </p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
}
