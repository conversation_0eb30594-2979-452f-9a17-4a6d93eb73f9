"use server";

import prisma from "@/lib/prisma";

interface SaveConversationData {
  userId: string;
  userMessage: string;
  imageData?: string;
  imageType?: string;
  aiResponse: string;
}

export async function saveConversation(data: SaveConversationData) {
  try {
    const conversation = await prisma.conversation.create({
      data: {
        userId: data.userId,
        userMessage: data.userMessage,
        imageData: data.imageData,
        imageType: data.imageType,
        aiResponse: data.aiResponse,
      },
    });

    return {
      success: true,
      conversation,
    };
  } catch (error) {
    console.error("Error saving conversation:", error);
    return {
      success: false,
      error: "Failed to save conversation",
    };
  }
}

export async function getUserConversations(userId: string, limit = 50) {
  try {
    const conversations = await prisma.conversation.findMany({
      where: {
        userId,
      },
      orderBy: {
        createdAt: "desc",
      },
      take: limit,
    });

    return {
      success: true,
      conversations,
    };
  } catch (error) {
    console.error("Error fetching conversations:", error);
    return {
      success: false,
      error: "Failed to fetch conversations",
    };
  }
}

export async function deleteConversation(conversationId: string, userId: string) {
  try {
    // Ensure the conversation belongs to the user
    const conversation = await prisma.conversation.findFirst({
      where: {
        id: conversationId,
        userId,
      },
    });

    if (!conversation) {
      return {
        success: false,
        error: "Conversation not found or unauthorized",
      };
    }

    await prisma.conversation.delete({
      where: {
        id: conversationId,
      },
    });

    return {
      success: true,
    };
  } catch (error) {
    console.error("Error deleting conversation:", error);
    return {
      success: false,
      error: "Failed to delete conversation",
    };
  }
}
