import prisma from "@/lib/prisma";
import { SubscriptionStatus } from "@prisma/client";

export const createSubscription = async (data: {
  userId: string;
  stripeSubscriptionId: string;
  stripePriceId: string;
  status: SubscriptionStatus;
  currentPeriodStart: Date;
  currentPeriodEnd: Date;
}) => {
  try {
    const subscription = await prisma.subscription.create({
      data,
      include: {
        user: true,
      },
    });
    return subscription;
  } catch (error) {
    console.error("🚀 ~ createSubscription ~ error:", error);
    return null;
  }
};

export const updateSubscription = async (
  stripeSubscriptionId: string,
  data: {
    status?: SubscriptionStatus;
    currentPeriodStart?: Date;
    currentPeriodEnd?: Date;
    cancelAtPeriodEnd?: boolean;
  }
) => {
  try {
    const subscription = await prisma.subscription.update({
      where: {
        stripeSubscriptionId,
      },
      data,
      include: {
        user: true,
      },
    });
    return subscription;
  } catch (error) {
    console.error("🚀 ~ updateSubscription ~ error:", error);
    return null;
  }
};

export const getSubscriptionByStripeId = async (stripeSubscriptionId: string) => {
  try {
    const subscription = await prisma.subscription.findUnique({
      where: {
        stripeSubscriptionId,
      },
      include: {
        user: true,
      },
    });
    return subscription;
  } catch (error) {
    console.error("🚀 ~ getSubscriptionByStripeId ~ error:", error);
    return null;
  }
};

export const getUserActiveSubscription = async (userId: string) => {
  try {
    const subscription = await prisma.subscription.findFirst({
      where: {
        userId,
        status: "ACTIVE",
      },
      include: {
        user: true,
      },
      orderBy: {
        createdAt: "desc",
      },
    });
    return subscription;
  } catch (error) {
    console.error("🚀 ~ getUserActiveSubscription ~ error:", error);
    return null;
  }
};

export const cancelSubscription = async (stripeSubscriptionId: string) => {
  try {
    const subscription = await prisma.subscription.update({
      where: {
        stripeSubscriptionId,
      },
      data: {
        status: "CANCELED",
        cancelAtPeriodEnd: true,
      },
    });
    return subscription;
  } catch (error) {
    console.error("🚀 ~ cancelSubscription ~ error:", error);
    return null;
  }
};
