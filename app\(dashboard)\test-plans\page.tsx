"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { createTestPlans } from "@/actions/create-test-plans";

export default function TestPlansPage() {
  const [result, setResult] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(false);

  const handleCreatePlans = async () => {
    setIsLoading(true);
    try {
      const result = await createTestPlans();
      setResult(result);
    } catch (error) {
      setResult({ error: "Failed to create plans", details: error });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="p-6">
      <h1 className="text-2xl font-bold mb-4">Test Plans Creation</h1>
      
      <Button 
        onClick={handleCreatePlans}
        disabled={isLoading}
        className="mb-4"
      >
        {isLoading ? "Creating..." : "Create Test Plans"}
      </Button>

      {result && (
        <div className="bg-gray-100 p-4 rounded">
          <h2 className="font-bold mb-2">Result:</h2>
          <pre className="text-sm overflow-auto">
            {JSON.stringify(result, null, 2)}
          </pre>
        </div>
      )}
    </div>
  );
}
