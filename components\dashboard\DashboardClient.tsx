"use client";

import { useRouter } from "next/navigation";
import DashboardWrapper from "./DashboardWrapper";
import { ChatHistoryItem } from "@/types/dashboard";
import { useConversation } from "@/contexts/ConversationContext";

interface DashboardClientProps {
  children: React.ReactNode;
  chatHistory: ChatHistoryItem[];
  userEmail: string;
  userName: string;
  userImage?: string;
}

export default function DashboardClient({
  children,
  chatHistory,
  userEmail,
  userName,
  userImage,
}: DashboardClientProps) {
  const router = useRouter();
  const { setSelectedConversation, setIsNewChat } = useConversation();

  const handleConversationClick = (conversation: ChatHistoryItem) => {
    // Set the selected conversation in context
    setSelectedConversation(conversation);
    setIsNewChat(false);

    // Navigate to chat page
    router.push("/chat");
  };

  const handleNewChatClick = () => {
    // Clear any selected conversation and set new chat flag
    setSelectedConversation(null);
    setIsNewChat(true);

    // Navigate to chat page
    router.push("/chat");
  };

  return (
    <DashboardWrapper
      chatHistory={chatHistory}
      userEmail={userEmail}
      userName={userName}
      userImage={userImage}
      onConversationClick={handleConversationClick}
      onNewChatClick={handleNewChatClick}>
      {children}
    </DashboardWrapper>
  );
}
