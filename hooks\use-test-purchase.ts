import { useMutation, useQueryClient } from "@tanstack/react-query";

async function testPurchasePlan(planId: string) {
  const response = await fetch("/api/test/purchase-plan", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify({ planId }),
  });

  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.error || "Failed to purchase plan");
  }

  return response.json();
}

export function useTestPurchase() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: testPurchasePlan,
    onSuccess: (data) => {
      // Invalidate and refetch user readings to update the UI
      queryClient.invalidateQueries({ queryKey: ["userReadings"] });
      
      // Show success message
      alert(`✅ Test Purchase Successful!\n\nPlan: ${data.plan.name}\nReadings Limit: ${data.plan.readingsLimit}\n\nThis is a test purchase. In production, this would go through Stripe.`);
    },
    onError: (error) => {
      console.error("Test purchase failed:", error);
      alert(`❌ Test Purchase Failed: ${error.message}`);
    },
  });
}
