import prisma from "@/lib/prisma";

export const getUserById = async (id: string) => {
  try {
    const user = await prisma.user.findUnique({
      where: {
        id,
      },
    });
    return user;
  } catch (error) {
    console.log("🚀 ~ getUserById ~ error:", error);
    return null;
  }
};

export const getUserWithPlan = async (id: string) => {
  try {
    const user = await prisma.user.findUnique({
      where: {
        id,
      },
      include: {
        currentPlan: true,
      },
    });
    return user;
  } catch (error) {
    console.log("🚀 ~ getUserWithPlan ~ error:", error);
    return null;
  }
};
