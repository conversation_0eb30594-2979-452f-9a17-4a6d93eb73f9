"use client";

import { useQuery } from "@tanstack/react-query";
import { Button } from "@/components/ui/button";
import { useState } from "react";

async function fetchPlansDebug() {
  const response = await fetch("/api/plans");
  if (!response.ok) {
    throw new Error("Failed to fetch plans");
  }
  return response.json();
}

export default function DebugPlansPage() {
  const { data: plans, isLoading, error, refetch } = useQuery({
    queryKey: ["plans-debug"],
    queryFn: fetchPlansDebug,
  });

  const [createResult, setCreateResult] = useState<any>(null);

  const createPlans = async () => {
    try {
      const response = await fetch("/api/admin/seed-plans", {
        method: "POST",
      });
      const result = await response.json();
      setCreateResult(result);
      refetch(); // Refresh the plans list
    } catch (error) {
      setCreateResult({ error: "Failed to create plans" });
    }
  };

  return (
    <div className="p-6 max-w-4xl mx-auto">
      <h1 className="text-2xl font-bold mb-6">Plans Debug Page</h1>
      
      <div className="mb-6">
        <Button onClick={createPlans} className="mr-4">
          Create Missing Plans
        </Button>
        <Button onClick={() => refetch()} variant="outline">
          Refresh Plans
        </Button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Current Plans */}
        <div className="bg-white p-6 rounded-lg shadow">
          <h2 className="text-xl font-semibold mb-4">Current Plans in Database</h2>
          
          {isLoading && <p>Loading...</p>}
          {error && <p className="text-red-500">Error: {error.message}</p>}
          
          {plans && (
            <div>
              <p className="mb-4">Found {plans.length} plans:</p>
              <div className="space-y-3">
                {plans.map((plan: any) => (
                  <div key={plan.id} className="border p-3 rounded">
                    <h3 className="font-semibold">{plan.nameAr} ({plan.name})</h3>
                    <p className="text-sm text-gray-600">
                      Price: ${plan.price} | Readings: {plan.readingsLimit}
                    </p>
                    <p className="text-xs text-gray-500">
                      ID: {plan.id}
                    </p>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>

        {/* Create Result */}
        <div className="bg-white p-6 rounded-lg shadow">
          <h2 className="text-xl font-semibold mb-4">Creation Result</h2>
          
          {createResult && (
            <pre className="bg-gray-100 p-3 rounded text-sm overflow-auto">
              {JSON.stringify(createResult, null, 2)}
            </pre>
          )}
        </div>
      </div>

      <div className="mt-8 bg-blue-50 p-4 rounded-lg">
        <h3 className="font-semibold text-blue-800 mb-2">Expected Plans:</h3>
        <ul className="text-blue-700 text-sm space-y-1">
          <li>• Free Plan (الباقة المجانية) - $0 - 1 reading</li>
          <li>• Starter Plan (باقة البداية) - $3 - 5 readings</li>
          <li>• Basic Plan (الباقة الأساسية) - $25 - 10 readings</li>
          <li>• Premium Plan (الباقة المتقدمة) - $35 - Unlimited readings</li>
        </ul>
      </div>
    </div>
  );
}
