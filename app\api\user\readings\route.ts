import { auth } from "@/auth";
import prisma from "@/lib/prisma";
import { NextResponse } from "next/server";

export async function GET() {
  try {
    const session = await auth();
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    const user = await prisma.user.findUnique({
      where: {
        id: session.user.id,
      },
      select: {
        id: true,
        readingsUsed: true,
        readingsLimit: true,
        subscriptionStatus: true,
        currentPlan: {
          select: {
            id: true,
            name: true,
            nameAr: true,
            readingsLimit: true,
          },
        },
      },
    });

    if (!user) {
      return NextResponse.json(
        { error: "User not found" },
        { status: 404 }
      );
    }

    const remainingReadings = user.readingsLimit - user.readingsUsed;

    return NextResponse.json({
      readingsUsed: user.readingsUsed,
      readingsLimit: user.readingsLimit,
      remainingReadings: Math.max(0, remainingReadings),
      subscriptionStatus: user.subscriptionStatus,
      currentPlan: user.currentPlan,
    });
  } catch (error) {
    console.error("Error fetching user readings:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
