import { useMutation } from "@tanstack/react-query";
import { redirectToCheckout } from "@/lib/stripe-client";

interface CreateCheckoutSessionResponse {
  sessionId: string;
  url: string;
}

async function createCheckoutSession(
  planId: string,
): Promise<CreateCheckoutSessionResponse> {
  const response = await fetch("/api/stripe/create-checkout-session", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify({ planId }),
  });

  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.error || "Failed to create checkout session");
  }

  return response.json();
}

export function usePurchasePlan() {
  return useMutation({
    mutationFn: async (planId: string) => {
      try {
        const { sessionId } = await createCheckoutSession(planId);
        await redirectToCheckout(sessionId);
      } catch (error: any) {
        console.error("Checkout error:", error);
        // For development/testing, show an alert instead of failing silently
        alert(
          `Test Mode: Would redirect to Stripe checkout for plan ${planId}. Error: ${error.message}`,
        );
        throw error;
      }
    },
    onError: (error) => {
      console.error("Failed to purchase plan:", error);
    },
  });
}
