# Stripe Integration Setup

This guide will help you set up Stripe integration for the Fenjeni coffee cup reading application.

## Prerequisites

1. A Stripe account (sign up at https://stripe.com)
2. Access to your Stripe Dashboard

## Step 1: Get Stripe API Keys

1. Log in to your Stripe Dashboard
2. Go to Developers > API keys
3. Copy your **Publishable key** and **Secret key**
4. Add them to your `.env.local` file:

```env
STRIPE_SECRET_KEY=sk_test_your_secret_key_here
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_your_publishable_key_here
```

## Step 2: Create Products and Prices

1. Go to Products in your Stripe Dashboard
2. Create products for each plan:

### Starter Plan
- Name: "Starter Plan"
- Description: "5 coffee cup readings per month"
- Price: $3.00 USD (recurring monthly)

### Basic Plan  
- Name: "Basic Plan"
- Description: "10 coffee cup readings per month"
- Price: $25.00 USD (recurring monthly)

### Premium Plan
- Name: "Premium Plan" 
- Description: "Unlimited coffee cup readings"
- Price: $35.00 USD (recurring monthly)

3. Copy the **Price IDs** (they start with `price_`) for each plan

## Step 3: Update Plan Price IDs

1. Run the seed plans action or manually update the database
2. Replace the placeholder price IDs in `actions/seed-plans.ts` with your actual Stripe price IDs:

```typescript
stripePriceId: "price_your_actual_stripe_price_id_here"
```

## Step 4: Set Up Webhooks

1. Go to Developers > Webhooks in your Stripe Dashboard
2. Click "Add endpoint"
3. Set the endpoint URL to: `https://yourdomain.com/api/webhooks/stripe`
4. Select these events to listen for:
   - `checkout.session.completed`
   - `customer.subscription.created`
   - `customer.subscription.updated`
   - `customer.subscription.deleted`
   - `invoice.payment_succeeded`
   - `invoice.payment_failed`

5. Copy the webhook signing secret and add it to your `.env.local`:

```env
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret_here
```

## Step 5: Test the Integration

1. Start your development server
2. Navigate to `/plans` in your application
3. Try purchasing a plan (use Stripe test card: 4242 4242 4242 4242)
4. Verify that:
   - Checkout session is created
   - Payment is processed
   - User's plan is updated in the database
   - Reading limits are updated

## Test Cards

Use these test card numbers for testing:

- **Success**: 4242 4242 4242 4242
- **Declined**: 4000 0000 0000 0002
- **Requires authentication**: 4000 0025 0000 3155

## Production Setup

1. Switch to live API keys in production
2. Update webhook endpoint to your production domain
3. Test with real payment methods
4. Monitor webhook delivery in Stripe Dashboard

## Troubleshooting

- Check webhook logs in Stripe Dashboard for delivery issues
- Verify environment variables are set correctly
- Check server logs for error messages
- Ensure webhook endpoint is publicly accessible
