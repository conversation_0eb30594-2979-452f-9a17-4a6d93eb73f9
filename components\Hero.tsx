"use client";
import Image from "next/image";
import Link from "next/link";

export default function Hero() {
  return (
    <section className="relative min-h-screen flex items-center justify-center overflow-hidden">
      {/* Background Image */}
      <div className="absolute inset-0 z-0">
        <Image
          src="/hero-image.png"
          alt="Hero Background"
          fill
          className="object-cover object-center"
          priority
        />
        {/* Overlay for better text readability */}
        <div className="absolute inset-0 bg-black/20"></div>
      </div>

      {/* Mobile Content Container */}
      <div className="absolute z-10 inset-0 flex flex-col items-center justify-center px-4 text-center md:hidden">
        <div className="max-w-sm">
          {/* Mobile Main Heading */}
          <h1 className="text-3xl sm:text-4xl font-bold mb-4 leading-tight text-white text-right">
            <span className="block mb-2">صوّر فنجانك...</span>
          </h1>

          {/* Mobile Subheading */}
          <h2 className="text-2xl sm:text-3xl mb-6 leading-relaxed text-white text-right">
            واكتشف ما يخبئه لك القدر
          </h2>

          {/* Mobile Call to Action Button */}
          <div className="flex justify-center">
            <Link
              href="/signup"
              className="inline-flex items-center px-6 py-3 font-bold bg-[#E5B565] text-base rounded-full transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl text-black">
              جرب مجانا
            </Link>
          </div>
        </div>
      </div>

      {/* Desktop Content Container - Hidden on mobile */}
      <div
        className="absolute z-10 hidden md:block"
        style={{ top: "200px", left: "100px" }}>
        <div className="max-w-4xl">
          {/* Desktop Main Heading */}
          <h1 className="text-[64px] font-bold mb-6 leading-tight !text-right">
            <span className="block mb-2">صوّر فنجانك...</span>
          </h1>

          {/* Desktop Subheading */}
          <h2 className="text-[64px] mb-8 leading-relaxed">
            واكتشف ما يخبئه لك القدر
          </h2>

          {/* Desktop Call to Action Button */}
          <div className="flex justify-start">
            <Link
              href="/signup"
              className="inline-flex items-center px-8 py-4 font-bold !bg-[#E5B565] text-lg rounded-full transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl">
              جرب مجانا
            </Link>
          </div>
        </div>
      </div>
    </section>
  );
}
