"use client";

import React, { createContext, useContext, useState } from "react";
import { ChatHistoryItem, Message } from "@/types/dashboard";

interface ConversationContextType {
  selectedConversation: ChatHistoryItem | null;
  setSelectedConversation: (conversation: ChatHistoryItem | null) => void;
  loadConversationMessages: (conversation: ChatHistoryItem) => Message[];
  isNewChat: boolean;
  setIsNewChat: (isNew: boolean) => void;
}

const ConversationContext = createContext<ConversationContextType | undefined>(
  undefined,
);

export function ConversationProvider({
  children,
}: {
  children: React.ReactNode;
}) {
  const [selectedConversation, setSelectedConversation] =
    useState<ChatHistoryItem | null>(null);
  const [isNewChat, setIsNewChat] = useState(false);

  const loadConversationMessages = (
    conversation: ChatHistoryItem,
  ): Message[] => {
    const messages: Message[] = [
      {
        id: Date.now() - 1,
        content: conversation.userMessage,
        sender: "user",
        timestamp: conversation.createdAt,
        image: conversation.imageData,
        imageType: conversation.imageType,
      },
      {
        id: Date.now(),
        content: conversation.aiResponse,
        sender: "assistant",
        timestamp: conversation.createdAt,
      },
    ];

    return messages;
  };

  return (
    <ConversationContext.Provider
      value={{
        selectedConversation,
        setSelectedConversation,
        loadConversationMessages,
        isNewChat,
        setIsNewChat,
      }}>
      {children}
    </ConversationContext.Provider>
  );
}

export function useConversation() {
  const context = useContext(ConversationContext);
  if (context === undefined) {
    throw new Error(
      "useConversation must be used within a ConversationProvider",
    );
  }
  return context;
}
