"use client";

import { useState } from "react";
import { Button } from "@/components/ui/button";
import { useCheckoutCompletion } from "@/hooks/use-checkout-completion";

export default function TestCheckoutPage() {
  const [sessionId, setSessionId] = useState("");
  const [result, setResult] = useState<any>(null);
  const checkoutCompletionMutation = useCheckoutCompletion();

  const handleTestCompletion = () => {
    if (!sessionId.trim()) {
      alert("Please enter a session ID");
      return;
    }

    checkoutCompletionMutation.mutate(
      { sessionId: sessionId.trim() },
      {
        onSuccess: (data) => {
          setResult({ success: true, data });
        },
        onError: (error) => {
          setResult({ success: false, error: error.message });
        }
      }
    );
  };

  return (
    <div className="p-6 max-w-4xl mx-auto">
      <h1 className="text-2xl font-bold mb-6">Test Checkout Completion</h1>
      
      <div className="bg-white p-6 rounded-lg shadow mb-6">
        <h2 className="text-xl font-semibold mb-4">Test Checkout Session Completion</h2>
        
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium mb-2">
              Stripe Checkout Session ID:
            </label>
            <input
              type="text"
              value={sessionId}
              onChange={(e) => setSessionId(e.target.value)}
              placeholder="cs_test_..."
              className="w-full p-2 border rounded-md"
            />
          </div>
          
          <Button 
            onClick={handleTestCompletion}
            disabled={checkoutCompletionMutation.isPending}
            className="bg-blue-600 hover:bg-blue-700 text-white"
          >
            {checkoutCompletionMutation.isPending ? "Processing..." : "Complete Checkout"}
          </Button>
        </div>
      </div>

      {result && (
        <div className="bg-white p-6 rounded-lg shadow">
          <h3 className="text-lg font-semibold mb-4">
            Result: {result.success ? "Success" : "Error"}
          </h3>
          <pre className="bg-gray-100 p-4 rounded text-sm overflow-auto">
            {JSON.stringify(result, null, 2)}
          </pre>
        </div>
      )}

      <div className="mt-8 bg-blue-50 p-4 rounded-lg">
        <h3 className="font-semibold text-blue-800 mb-2">How to test:</h3>
        <ol className="text-blue-700 text-sm space-y-1 list-decimal list-inside">
          <li>Go to /plans and purchase a plan</li>
          <li>Complete the Stripe checkout (use test card: 4242 4242 4242 4242)</li>
          <li>Copy the session ID from the success URL</li>
          <li>Paste it here and click "Complete Checkout"</li>
          <li>Verify that the subscription is created and user plan is updated</li>
        </ol>
      </div>
    </div>
  );
}
