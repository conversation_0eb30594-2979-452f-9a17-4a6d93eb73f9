"use server";

import { auth } from "@/auth";
import { redirect } from "next/navigation";

export async function purchasePlan(planId: string) {
  try {
    const session = await auth();
    
    if (!session?.user?.id) {
      redirect("/login");
    }

    // Create checkout session
    const response = await fetch(`${process.env.NEXT_PUBLIC_APP_URL}/api/stripe/create-checkout-session`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ planId }),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error || "Failed to create checkout session");
    }

    const { url } = await response.json();
    
    if (url) {
      redirect(url);
    } else {
      throw new Error("No checkout URL received");
    }
  } catch (error) {
    console.error("Error purchasing plan:", error);
    throw error;
  }
}
