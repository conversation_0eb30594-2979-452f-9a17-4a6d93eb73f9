import Image from "next/image";

export default function TypingIndicator() {
  return (
    <div className="flex gap-2 sm:gap-3 lg:gap-4 justify-start">
      <div className="w-6 h-6 sm:w-8 sm:h-8 rounded-full overflow-hidden flex-shrink-0">
        <Image
          src="/morjana.jpg"
          alt="Manel"
          width={32}
          height={32}
          className="w-full h-full object-cover"
        />
      </div>
      <div className="bg-gray-100 dark:bg-gray-700 rounded-2xl px-3 py-2 sm:px-4 sm:py-3">
        <div className="flex space-x-1">
          <div className="w-1.5 h-1.5 sm:w-2 sm:h-2 bg-gray-400 rounded-full animate-bounce"></div>
          <div
            className="w-1.5 h-1.5 sm:w-2 sm:h-2 bg-gray-400 rounded-full animate-bounce"
            style={{ animationDelay: "0.1s" }}></div>
          <div
            className="w-1.5 h-1.5 sm:w-2 sm:h-2 bg-gray-400 rounded-full animate-bounce"
            style={{ animationDelay: "0.2s" }}></div>
        </div>
      </div>
    </div>
  );
}
