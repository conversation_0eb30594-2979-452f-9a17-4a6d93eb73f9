"use server";

import { signIn } from "@/auth";
import { AuthError } from "next-auth";

export async function googleAuthenticate(): Promise<void> {
  try {
    await signIn("google");
  } catch (error) {
    if (error instanceof AuthError) {
      console.error("Google login failed:", error);
      // Don't return anything for form actions
      return;
    }
    throw error;
  }
}

export async function googleRegister(): Promise<void> {
  try {
    await signIn("google");
  } catch (error) {
    if (error instanceof AuthError) {
      console.error("Google registration failed:", error);
      // Don't return anything for form actions
      return;
    }
    throw error;
  }
}
