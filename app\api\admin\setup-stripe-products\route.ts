import { NextResponse } from "next/server";
import { stripe } from "@/lib/stripe";
import prisma from "@/lib/prisma";

export async function POST() {
  try {
    console.log("Setting up Stripe products and prices...");

    // Define the plans to create in Stripe
    const plansToSetup = [
      {
        name: "Starter Plan",
        nameAr: "باقة البداية",
        description:
          "Perfect for beginners with 5 coffee cup readings per month",
        price: 3.0,
        readingsLimit: 5,
      },
      {
        name: "Basic Plan",
        nameAr: "الباقة الأساسية",
        description: "Most popular plan with 10 detailed readings per month",
        price: 25.0,
        readingsLimit: 10,
      },
      {
        name: "Premium Plan",
        nameAr: "الباقة المتقدمة",
        description: "For professionals with unlimited readings per month",
        price: 35.0,
        readingsLimit: 999999,
      },
    ];

    const results = [];

    for (const planData of plansToSetup) {
      try {
        // Create product in Stripe
        const product = await stripe.products.create({
          name: planData.name,
          description: planData.description,
          metadata: {
            nameAr: planData.nameAr,
            readingsLimit: planData.readingsLimit.toString(),
          },
        });

        console.log(`Created Stripe product: ${product.id} - ${planData.name}`);

        // Create price for the product
        const price = await stripe.prices.create({
          product: product.id,
          unit_amount: Math.round(planData.price * 100), // Convert to cents
          currency: "usd",
          recurring: {
            interval: "month",
          },
          metadata: {
            planName: planData.name,
            readingsLimit: planData.readingsLimit.toString(),
          },
        });

        console.log(`Created Stripe price: ${price.id} - $${planData.price}`);

        // Update the plan in our database with the Stripe price ID
        const updatedPlan = await prisma.plan.updateMany({
          where: { name: planData.name },
          data: { stripePriceId: price.id },
        });

        console.log(
          `Updated database plan: ${planData.name} with price ID: ${price.id}`,
        );

        results.push({
          planName: planData.name,
          productId: product.id,
          priceId: price.id,
          price: planData.price,
          databaseUpdated: updatedPlan.count > 0,
        });
      } catch (error: any) {
        console.error(`Error setting up plan ${planData.name}:`, error);
        results.push({
          planName: planData.name,
          error: error.message,
        });
      }
    }

    // Get all plans from database to verify
    const allPlans = await prisma.plan.findMany({
      orderBy: { price: "asc" },
    });

    return NextResponse.json({
      success: true,
      message: "Stripe products and prices setup completed",
      results,
      databasePlans: allPlans.map((p) => ({
        id: p.id,
        name: p.name,
        nameAr: p.nameAr,
        price: p.price,
        stripePriceId: p.stripePriceId,
        readingsLimit: p.readingsLimit,
      })),
    });
  } catch (error: any) {
    console.error("Error setting up Stripe products:", error);
    return NextResponse.json(
      {
        error: "Failed to setup Stripe products",
        details: error.message,
      },
      { status: 500 },
    );
  }
}
