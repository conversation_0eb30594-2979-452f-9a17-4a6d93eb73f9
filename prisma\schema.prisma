// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

enum Role {
  CUSTOMER
  ADMIN
}

enum SubscriptionStatus {
  ACTIVE
  CANCELED
  PAST_DUE
  UNPAID
  TRIAL
}

model User {
  id                 String              @id @default(cuid())
  email              String              @unique
  name               String?
  username           String?
  role               Role                @default(CUSTOMER)
  image              String?
  emailVerified      DateTime?
  password           String?
  stripeCustomerId   String?             @unique
  subscriptionStatus SubscriptionStatus? @default(TRIAL)
  readingsUsed       Int                 @default(0)
  readingsLimit      Int                 @default(1)
  currentPlanId      String?
  createdAt          DateTime            @default(now())
  updatedAt          DateTime            @updatedAt
  accounts           Account[]
  subscriptions      Subscription[]
  conversations      Conversation[]
  currentPlan        Plan?               @relation(fields: [currentPlanId], references: [id])
}

model Account {
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String?
  access_token      String?
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String?
  session_state     String?
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt
  user              User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@id([provider, providerAccountId])
}

model Subscription {
  id                   String             @id @default(cuid())
  userId               String
  stripeSubscriptionId String             @unique
  stripePriceId        String
  status               SubscriptionStatus
  currentPeriodStart   DateTime
  currentPeriodEnd     DateTime
  cancelAtPeriodEnd    Boolean            @default(false)
  createdAt            DateTime           @default(now())
  updatedAt            DateTime           @updatedAt
  user                 User               @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model Plan {
  id            String   @id @default(cuid())
  name          String
  nameAr        String
  description   String
  descriptionAr String
  price         Float
  currency      String   @default("USD")
  interval      String // monthly, yearly
  stripePriceId String   @unique
  features      String[] // Array of features
  featuresAr    String[] // Array of features in Arabic
  readingsLimit Int
  isActive      Boolean  @default(true)
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt
  users         User[]
}

model Conversation {
  id          String   @id @default(cuid())
  userId      String
  userMessage String // The user's message/question
  imageData   String? // Base64 image data (optional, for coffee cup images)
  imageType   String? // Image MIME type (e.g., "image/jpeg")
  aiResponse  String // The AI's response/reading
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  user        User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@index([createdAt])
}
