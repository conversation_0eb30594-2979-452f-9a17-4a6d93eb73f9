import { auth } from "@/auth";
import { NextRequest, NextResponse } from "next/server";
import {
  stripe,
  getStripeCustomerByEmail,
  createStripeCustomer,
  createCheckoutSession,
} from "@/lib/stripe";
import prisma from "@/lib/prisma";
import { getPlanById } from "@/data/plan";

export async function POST(request: NextRequest) {
  try {
    const session = await auth();

    if (!session?.user?.id || !session?.user?.email) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { planId } = await request.json();

    if (!planId) {
      return NextResponse.json(
        { error: "Plan ID is required" },
        { status: 400 },
      );
    }

    // Get the plan details
    const plan = await getPlanById(planId);
    if (!plan || !plan.isActive) {
      return NextResponse.json(
        { error: "Plan not found or inactive" },
        { status: 404 },
      );
    }

    // Don't allow checkout for free plans
    if (plan.price === 0) {
      return NextResponse.json(
        { error: "Cannot create checkout session for free plan" },
        { status: 400 },
      );
    }

    // Allow purchasing the same plan multiple times (renewals)

    // Get or create Stripe customer
    let stripeCustomer = await getStripeCustomerByEmail(session.user.email);

    if (!stripeCustomer) {
      stripeCustomer = await createStripeCustomer(
        session.user.email,
        session.user.name || undefined,
      );
    }

    // Update user with Stripe customer ID if not already set
    await prisma.user.update({
      where: { id: session.user.id },
      data: { stripeCustomerId: stripeCustomer.id },
    });

    // Get the base URL with proper scheme
    const baseUrl =
      process.env.NEXT_PUBLIC_APP_URL ||
      (process.env.NODE_ENV === "production"
        ? "https://yourdomain.com"
        : "http://localhost:3000");

    // Create checkout session
    const checkoutSession = await createCheckoutSession({
      customerId: stripeCustomer.id,
      priceId: plan.stripePriceId,
      successUrl: `${baseUrl}/chat?success=true&session_id={CHECKOUT_SESSION_ID}`,
      cancelUrl: `${baseUrl}/plans?canceled=true`,
      metadata: {
        userId: session.user.id,
        planId: plan.id,
      },
    });

    return NextResponse.json({
      sessionId: checkoutSession.id,
      url: checkoutSession.url,
    });
  } catch (error) {
    console.error("Error creating checkout session:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 },
    );
  }
}
