import DashboardWrapper from "@/components/dashboard/DashboardWrapper";
import { auth } from "@/auth";
import { ChatHistoryItem } from "@/types/dashboard";
import prisma from "@/lib/prisma";
import { ConversationProvider } from "@/contexts/ConversationContext";
import DashboardClient from "@/components/dashboard/DashboardClient";

// Function to format date in Arabic with Western numbers
function formatDate(date: Date): string {
  const now = new Date();
  const diffInHours = Math.floor(
    (now.getTime() - date.getTime()) / (1000 * 60 * 60),
  );

  // If it's today, show time
  if (diffInHours < 24 && date.getDate() === now.getDate()) {
    return date.toLocaleTimeString("en-US", {
      hour: "2-digit",
      minute: "2-digit",
      hour12: true,
    });
  }
  // If it's yesterday
  else if (diffInHours < 48 && date.getDate() === now.getDate() - 1) {
    return "أمس";
  }
  // If it's within this week, show day name
  else if (diffInHours < 168) {
    // 7 days
    return date.toLocaleDateString("ar-SA", {
      weekday: "long",
    });
  }
  // If it's older, show full date
  else {
    const day = date.getDate();
    const month = date.toLocaleDateString("ar-SA", { month: "short" });
    const year = date.getFullYear();

    if (year !== now.getFullYear()) {
      return `${day} ${month} ${year}`;
    } else {
      return `${day} ${month}`;
    }
  }
}

// Function to generate title from user message
function generateTitle(userMessage: string, hasImage: boolean): string {
  if (hasImage) {
    return "قراءة فنجان القهوة";
  }

  // Truncate long messages for title
  if (userMessage.length > 30) {
    return userMessage.substring(0, 30) + "...";
  }

  return userMessage;
}

// Fetch real chat history from database
async function getChatHistory(userId: string): Promise<ChatHistoryItem[]> {
  try {
    const conversations = await prisma.conversation.findMany({
      where: {
        userId,
      },
      orderBy: {
        createdAt: "desc",
      },
      take: 20, // Limit to last 20 conversations
    });

    return conversations.map((conv) => ({
      id: conv.id,
      title: generateTitle(conv.userMessage, !!conv.imageData),
      date: formatDate(conv.createdAt),
      userMessage: conv.userMessage,
      aiResponse: conv.aiResponse,
      imageData: conv.imageData || undefined,
      imageType: conv.imageType || undefined,
      createdAt: conv.createdAt,
    }));
  } catch (error) {
    console.error("Error fetching chat history:", error);
    return [];
  }
}

export default async function DashboardLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  // Get user session server-side
  const session = await auth();

  // Get chat history for the user
  const chatHistory = session?.user?.id
    ? await getChatHistory(session.user.id)
    : [];

  // Extract user info from session
  const userEmail = session?.user?.email || "<EMAIL>";
  const userName = session?.user?.name || "المستخدم";
  const userImage = session?.user?.image || undefined;

  return (
    <ConversationProvider>
      <DashboardClient
        chatHistory={chatHistory}
        userEmail={userEmail}
        userName={userName}
        userImage={userImage}>
        {children}
      </DashboardClient>
    </ConversationProvider>
  );
}
