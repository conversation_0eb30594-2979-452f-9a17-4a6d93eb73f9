"use server";

import prisma from "@/lib/prisma";
import { getFreePlan } from "@/data/plan";

export async function assignFreePlanToUser(userId: string) {
  try {
    // Get the free plan
    const freePlan = await getFreePlan();
    
    if (!freePlan) {
      console.error("Free plan not found in database");
      return { success: false, error: "Free plan not found" };
    }

    // Update the user with the free plan
    const updatedUser = await prisma.user.update({
      where: {
        id: userId,
      },
      data: {
        currentPlanId: freePlan.id,
        readingsLimit: freePlan.readingsLimit,
        readingsUsed: 0,
        subscriptionStatus: "TRIAL",
      },
    });

    return { 
      success: true, 
      user: updatedUser,
      plan: freePlan 
    };
  } catch (error) {
    console.error("🚀 ~ assignFreePlanToUser ~ error:", error);
    return { 
      success: false, 
      error: "Failed to assign free plan to user" 
    };
  }
}

export async function createFreePlanIfNotExists() {
  try {
    // Check if free plan already exists
    const existingFreePlan = await getFreePlan();
    
    if (existingFreePlan) {
      return { success: true, plan: existingFreePlan };
    }

    // Create the free plan
    const freePlan = await prisma.plan.create({
      data: {
        name: "Free Plan",
        nameAr: "الباقة المجانية",
        description: "Get started with one free coffee cup reading",
        descriptionAr: "ابدأ مع قراءة فنجان قهوة مجانية واحدة",
        price: 0,
        currency: "USD",
        interval: "lifetime",
        stripePriceId: "free_plan_no_stripe",
        features: [
          "1 free coffee cup reading",
          "Basic symbol analysis",
          "Instant results"
        ],
        featuresAr: [
          "قراءة فنجان قهوة مجانية واحدة",
          "تحليل أساسي للرموز",
          "نتائج فورية"
        ],
        readingsLimit: 1,
        isActive: true,
      },
    });

    return { success: true, plan: freePlan };
  } catch (error) {
    console.error("🚀 ~ createFreePlanIfNotExists ~ error:", error);
    return { 
      success: false, 
      error: "Failed to create free plan" 
    };
  }
}
