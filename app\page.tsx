import Hero from "@/components/Hero";
import PricingSection from "@/components/PricingSection";
import HowItWorksSection from "@/components/HowItWorksSection";
import FAQSection from "@/components/FAQSection";
import Image from "next/image";
import Header from "@/components/Header";

export default function Home() {
  return (
    <main>
      <Header />
      <Hero />
      <PricingSection />

      {/* Background with decorative shape */}
      <div className="relative">
        <div className="absolute inset-0 flex items-center justify-center -top-56 z-0">
          <Image
            src="/shape2.png"
            alt="Decorative Background"
            width={1758}
            height={750}
            className="w-[1500px] h-[750px] object-cover object-center opacity-40"
          />
        </div>
        <div className="relative z-10">
          <HowItWorksSection />
        </div>
      </div>

      <FAQSection />

      {/* Content Section */}
    </main>
  );
}
