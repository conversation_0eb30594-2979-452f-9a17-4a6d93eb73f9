"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { useCheckoutCompletion } from "@/hooks/use-checkout-completion";
import { useUserReadings } from "@/hooks/use-user-readings";

export default function DebugCheckoutPage() {
  const [sessionId, setSessionId] = useState("");
  const [result, setResult] = useState<any>(null);
  
  const { data: userReadings, refetch: refetchUserReadings } = useUserReadings();
  const checkoutCompletionMutation = useCheckoutCompletion();

  const handleTestCompletion = () => {
    if (!sessionId.trim()) {
      alert("Please enter a session ID");
      return;
    }

    setResult(null);
    
    checkoutCompletionMutation.mutate(
      { sessionId: sessionId.trim() },
      {
        onSuccess: (data) => {
          setResult({ success: true, data });
          refetchUserReadings(); // Refresh user data
        },
        onError: (error) => {
          setResult({ success: false, error: error.message });
        }
      }
    );
  };

  const clearSessionStorage = () => {
    sessionStorage.clear();
    alert("Session storage cleared!");
  };

  return (
    <div className="p-6 max-w-4xl mx-auto">
      <h1 className="text-2xl font-bold mb-6">Debug Checkout Completion</h1>
      
      {/* Current User Info */}
      <div className="bg-white p-6 rounded-lg shadow mb-6">
        <h2 className="text-xl font-semibold mb-4">Current User Info</h2>
        {userReadings ? (
          <div className="space-y-2">
            <p><strong>Readings Used:</strong> {userReadings.readingsUsed}</p>
            <p><strong>Readings Limit:</strong> {userReadings.readingsLimit}</p>
            <p><strong>Remaining:</strong> {userReadings.remainingReadings}</p>
            <p><strong>Subscription Status:</strong> {userReadings.subscriptionStatus}</p>
            <p><strong>Current Plan:</strong> {userReadings.currentPlan?.nameAr || "None"}</p>
          </div>
        ) : (
          <p>Loading user data...</p>
        )}
        <Button 
          onClick={() => refetchUserReadings()} 
          className="mt-4 bg-blue-600 hover:bg-blue-700 text-white"
        >
          Refresh User Data
        </Button>
      </div>

      {/* Test Checkout Completion */}
      <div className="bg-white p-6 rounded-lg shadow mb-6">
        <h2 className="text-xl font-semibold mb-4">Test Checkout Session Completion</h2>
        
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium mb-2">
              Stripe Checkout Session ID:
            </label>
            <input
              type="text"
              value={sessionId}
              onChange={(e) => setSessionId(e.target.value)}
              placeholder="cs_test_..."
              className="w-full p-2 border rounded-md"
            />
          </div>
          
          <div className="flex gap-4">
            <Button 
              onClick={handleTestCompletion}
              disabled={checkoutCompletionMutation.isPending}
              className="bg-green-600 hover:bg-green-700 text-white"
            >
              {checkoutCompletionMutation.isPending ? "Processing..." : "Complete Checkout"}
            </Button>
            
            <Button 
              onClick={clearSessionStorage}
              variant="outline"
            >
              Clear Session Storage
            </Button>
          </div>
        </div>
      </div>

      {/* Result */}
      {result && (
        <div className="bg-white p-6 rounded-lg shadow mb-6">
          <h3 className="text-lg font-semibold mb-4">
            Result: {result.success ? "✅ Success" : "❌ Error"}
          </h3>
          <pre className="bg-gray-100 p-4 rounded text-sm overflow-auto">
            {JSON.stringify(result, null, 2)}
          </pre>
        </div>
      )}

      {/* Instructions */}
      <div className="bg-blue-50 p-4 rounded-lg">
        <h3 className="font-semibold text-blue-800 mb-2">How to test:</h3>
        <ol className="text-blue-700 text-sm space-y-1 list-decimal list-inside">
          <li>Go to /plans and purchase a plan</li>
          <li>Complete the Stripe checkout (use test card: 4242 4242 4242 4242)</li>
          <li>Copy the session ID from the success URL (cs_test_...)</li>
          <li>Paste it here and click "Complete Checkout"</li>
          <li>Check that user data is updated correctly</li>
          <li>If you get errors, clear session storage and try again</li>
        </ol>
      </div>
    </div>
  );
}
