"use client";

import { useUserReadings } from "@/hooks/use-user-readings";
import { IconCoffee, IconLoader2 } from "@tabler/icons-react";
import Link from "next/link";

export default function ReadingsCounter() {
  const { data: userReadings, isLoading, error } = useUserReadings();

  if (isLoading) {
    return (
      <div className="p-4 border-t border-gray-700">
        <div className="flex items-center gap-2 text-gray-400">
          <IconLoader2 className="h-4 w-4 animate-spin" />
          <span className="text-sm">جاري التحميل...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-4 border-t border-gray-700">
        <div className="text-red-400 text-sm">
          خطأ في تحميل البيانات
        </div>
      </div>
    );
  }

  if (!userReadings) {
    return null;
  }

  const { remainingReadings, readingsLimit, currentPlan } = userReadings;
  const isLowOnReadings = remainingReadings <= 1;
  const hasNoReadings = remainingReadings === 0;

  return (
    <div className="p-4 border-t border-gray-700">
      <div className="space-y-3">
        {/* Readings Counter */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <IconCoffee className="h-4 w-4 text-amber-500" />
            <span className="text-sm text-gray-300">القراءات المتبقية</span>
          </div>
          <div className={`text-sm font-bold ${
            hasNoReadings 
              ? "text-red-400" 
              : isLowOnReadings 
                ? "text-yellow-400" 
                : "text-green-400"
          }`}>
            {remainingReadings} / {readingsLimit}
          </div>
        </div>

        {/* Progress Bar */}
        <div className="w-full bg-gray-700 rounded-full h-2">
          <div
            className={`h-2 rounded-full transition-all duration-300 ${
              hasNoReadings
                ? "bg-red-500"
                : isLowOnReadings
                  ? "bg-yellow-500"
                  : "bg-green-500"
            }`}
            style={{
              width: `${Math.max(0, (remainingReadings / readingsLimit) * 100)}%`,
            }}
          />
        </div>

        {/* Plan Info */}
        {currentPlan && (
          <div className="text-xs text-gray-400">
            الباقة الحالية: {currentPlan.nameAr}
          </div>
        )}

        {/* Warning or Upgrade Message */}
        {hasNoReadings ? (
          <div className="bg-red-900/20 border border-red-700 rounded-lg p-3">
            <p className="text-red-400 text-xs mb-2">
              لقد استنفدت جميع القراءات المتاحة
            </p>
            <Link
              href="/plans"
              className="inline-block bg-red-600 hover:bg-red-700 text-white text-xs px-3 py-1 rounded-md transition-colors"
            >
              ترقية الباقة
            </Link>
          </div>
        ) : isLowOnReadings ? (
          <div className="bg-yellow-900/20 border border-yellow-700 rounded-lg p-3">
            <p className="text-yellow-400 text-xs mb-2">
              قراءة واحدة متبقية فقط
            </p>
            <Link
              href="/plans"
              className="inline-block bg-yellow-600 hover:bg-yellow-700 text-white text-xs px-3 py-1 rounded-md transition-colors"
            >
              ترقية الباقة
            </Link>
          </div>
        ) : null}
      </div>
    </div>
  );
}
