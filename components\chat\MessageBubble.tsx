import { IconUser } from "@tabler/icons-react";
import { Message } from "@/types/dashboard";
import Image from "next/image";

interface MessageBubbleProps {
  message: Message;
}

export default function MessageBubble({ message }: MessageBubbleProps) {
  return (
    <div
      className={`flex gap-2 sm:gap-3 lg:gap-4 ${
        message.sender === "user" ? "justify-end" : "justify-start"
      }`}>
      {message.sender === "assistant" && (
        <div className="w-6 h-6 sm:w-8 sm:h-8 rounded-full overflow-hidden flex-shrink-0">
          <Image
            src="/morjana.jpg"
            alt="Morjana"
            width={32}
            height={32}
            className="w-full h-full object-cover"
          />
        </div>
      )}

      <div
        className={`max-w-[85%] sm:max-w-[75%] lg:max-w-[70%] rounded-2xl px-3 py-2 sm:px-4 sm:py-3 ${
          message.sender === "user"
            ? "bg-amber-600 text-white"
            : "bg-gray-100 dark:bg-gray-700 text-gray-900 dark:text-white"
        }`}>
        {/* Display image if present */}
        {message.image && (
          <div className="mb-2 sm:mb-3">
            <img
              src={message.image}
              alt="Uploaded image"
              className="max-w-full h-auto rounded-lg border border-gray-200 dark:border-gray-600"
              style={{ maxHeight: "250px" }}
            />
          </div>
        )}

        {/* Display text content */}
        {message.content && (
          <p className="text-xs sm:text-sm leading-relaxed whitespace-pre-wrap">
            {message.content}
          </p>
        )}

        <p
          className={`text-xs mt-1 sm:mt-2 ${
            message.sender === "user"
              ? "text-amber-100"
              : "text-gray-500 dark:text-gray-400"
          }`}>
          {message.timestamp.toLocaleTimeString("ar-SA", {
            hour: "2-digit",
            minute: "2-digit",
          })}
        </p>
      </div>

      {message.sender === "user" && (
        <div className="w-6 h-6 sm:w-8 sm:h-8 bg-gray-600 rounded-full flex items-center justify-center flex-shrink-0">
          <IconUser className="w-3 h-3 sm:w-4 sm:h-4 text-white" />
        </div>
      )}
    </div>
  );
}
