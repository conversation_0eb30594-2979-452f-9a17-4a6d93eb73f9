import authConfig from "./auth.config";
import NextAuth from "next-auth";
import {
  publicRoutes,
  privateRoutes,
  authRoutes,
  customerRoutes,
  publicApiRoutes,
} from "./routes";
import { getUserById } from "./data/user";

const { auth } = NextAuth(authConfig);

export default auth(async (req) => {
  const isLoggedIn = !!req.auth;
  const { nextUrl } = req;
  const url = process.env.NEXT_PUBLIC_APP_URL || "http://localhost:3000";

  // Check if the current path is a public route
  const isPublicRoute = publicRoutes.includes(nextUrl.pathname);

  // Check if the current path is a private route
  const isPrivateRoute = privateRoutes.some(
    (route) => nextUrl.pathname === route || nextUrl.pathname.startsWith(route),
  );

  // Check if the current path is an auth route (login/register)
  const isAuthRoute = authRoutes.includes(nextUrl.pathname);

  // Check if the current path is an API route
  const isApiRoute = nextUrl.pathname.startsWith("/api");

  // Check if it's a public API route
  const isPublicApiRoute = publicApiRoutes.some((route) =>
    nextUrl.pathname.startsWith(route),
  );

  // Allow API routes to pass through
  if (isApiRoute) {
    // Allow public API routes without authentication
    if (isPublicApiRoute) {
      return;
    }
    // For other API routes, continue with auth check
  }

  // If user is logged in and trying to access auth routes, redirect to chat
  if (isLoggedIn && isAuthRoute) {
    return Response.redirect(`${url}/chat`);
  }

  // If user is not logged in and trying to access auth routes, allow access
  if (!isLoggedIn && isAuthRoute) {
    return;
  }

  // If user is not logged in and trying to access private routes, redirect to login
  if (!isLoggedIn && isPrivateRoute) {
    return Response.redirect(`${url}/login`);
  }

  // If user is logged in and trying to access private routes, check permissions
  if (isLoggedIn && isPrivateRoute && req.auth?.user?.id) {
    const user = await getUserById(req.auth.user.id);

    if (!user) {
      return Response.redirect(`${url}/login`);
    }

    // For now, all authenticated users (CUSTOMER role) can access all dashboard routes
    // You can add more role-based logic here in the future if needed
    if (user.role === "CUSTOMER") {
      // Check if the route is accessible to customers
      const isCustomerRoute = customerRoutes.some(
        (route) =>
          nextUrl.pathname === route || nextUrl.pathname.startsWith(route),
      );

      if (!isCustomerRoute) {
        return Response.redirect(`${url}/chat`);
      }
    }
  }

  // Allow access to public routes
  if (isPublicRoute) {
    return;
  }
});

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder
     */
    "/((?!api|_next/static|_next/image|favicon.ico|public).*)",
  ],
};
