import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";

interface UserReadingsData {
  readingsUsed: number;
  readingsLimit: number;
  remainingReadings: number;
  subscriptionStatus: string;
  currentPlan: {
    id: string;
    name: string;
    nameAr: string;
    readingsLimit: number;
  } | null;
}

interface ChatResponse {
  success: boolean;
  response: string;
  readingsUsed: number;
  readingsLimit: number;
  remainingReadings: number;
}

interface ChatError {
  error: string;
  message?: string;
  remainingReadings?: number;
}

// Fetch user readings data
async function fetchUserReadings(): Promise<UserReadingsData> {
  const response = await fetch("/api/user/readings");

  if (!response.ok) {
    throw new Error("Failed to fetch user readings");
  }

  return response.json();
}

// Send chat message
async function sendChatMessage(messageData: {
  content: string;
  image?: { data: string; type: string };
}): Promise<ChatResponse> {
  const response = await fetch("/api/chat/send", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify({
      message: messageData.content,
      image: messageData.image,
    }),
  });

  const data = await response.json();

  if (!response.ok) {
    throw new Error(data.message || data.error || "Failed to send message");
  }

  return data;
}

export function useUserReadings() {
  return useQuery({
    queryKey: ["userReadings"],
    queryFn: fetchUserReadings,
    staleTime: 30 * 1000, // 30 seconds
    refetchOnWindowFocus: true,
  });
}

export function useSendMessage() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: sendChatMessage,
    onSuccess: (data) => {
      // Update the user readings cache with new data
      queryClient.setQueryData(
        ["userReadings"],
        (oldData: UserReadingsData | undefined) => {
          if (!oldData) return oldData;

          return {
            ...oldData,
            readingsUsed: data.readingsUsed,
            readingsLimit: data.readingsLimit,
            remainingReadings: data.remainingReadings,
          };
        },
      );
    },
    onError: (error) => {
      console.error("Failed to send message:", error);
      // Optionally refetch user readings to ensure data consistency
      queryClient.invalidateQueries({ queryKey: ["userReadings"] });
    },
  });
}
