"use client";
import Image from "next/image";
import Link from "next/link";
import { useState } from "react";
import {
  Navbar,
  NavBody,
  NavItems,
  MobileNav,
  MobileNavHeader,
  MobileNavMenu,
  MobileNavToggle,
  NavbarButton,
} from "@/components/ui/resizable-navbar";
import { Button } from "./ui/button";

export default function Header() {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  const toggleMobileMenu = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen);
  };

  const closeMobileMenu = () => {
    setIsMobileMenuOpen(false);
  };

  const navItems = [
    { name: "الرئيسية", link: "/" },
    { name: "العروض", link: "#offers" },
    { name: "الأسئلة", link: "#faq" },
  ];

  return (
    <Navbar className="fixed top-0 inset-x-0 z-50">
      {/* Desktop Navigation */}
      <NavBody className="bg-transparent border-none">
        {/* Logo */}
        <div className="flex-shrink-0">
          <Link href="/" className="flex items-center relative z-20">
            <Image
              src="/ferjeni_logo.png"
              alt="Fenjeni Logo"
              width={160}
              height={60}
              className="h-16 w-auto"
            />
          </Link>
        </div>

        {/* Navigation Items */}
        <NavItems items={navItems} className="rtl:space-x-reverse" />

        {/* Action Buttons */}
        <div className="hidden lg:flex items-center space-x-4 rtl:space-x-reverse gap-4 relative z-20">
          <Button
            asChild
            variant="secondary"
            className="text-amber-800 border border-amber-200 hover:bg-amber-50">
            <Link href="/login">تسجيل الدخول</Link>
          </Button>
          <Button
            asChild
            variant="default"
            className="bg-amber-500 text-white hover:bg-amber-600 border-amber-500">
            <Link href="/signup">سجل معنا</Link>
          </Button>
        </div>
      </NavBody>

      {/* Mobile Navigation */}
      <MobileNav className="bg-transparent border-none">
        <MobileNavHeader>
          {/* Mobile Logo */}
          <Link href="/" className="flex items-center">
            <Image
              src="/ferjeni_logo.png"
              alt="Fenjeni Logo"
              width={140}
              height={50}
              className="h-10 w-auto"
            />
          </Link>

          {/* Mobile Menu Toggle */}
          <MobileNavToggle
            isOpen={isMobileMenuOpen}
            onClick={toggleMobileMenu}
          />
        </MobileNavHeader>

        {/* Mobile Menu */}
        <MobileNavMenu isOpen={isMobileMenuOpen} onClose={closeMobileMenu}>
          <div className="flex flex-col space-y-4 w-full">
            {navItems.map((item, idx) => (
              <Link
                key={idx}
                href={item.link}
                onClick={closeMobileMenu}
                className="text-gray-700 hover:text-amber-600 px-3 py-2 text-base font-medium transition-colors">
                {item.name}
              </Link>
            ))}
            <div className="pt-4 border-t border-gray-200 flex flex-col space-y-2">
              <Link
                href="/login"
                onClick={closeMobileMenu}
                className="bg-amber-100 text-amber-800 hover:bg-amber-200 px-3 py-2 rounded-lg text-base font-medium transition-colors">
                تسجيل الدخول
              </Link>
              <Link
                href="/signup"
                onClick={closeMobileMenu}
                className="bg-amber-500 text-white hover:bg-amber-600 px-3 py-2 rounded-lg text-base font-medium transition-colors">
                سجل معنا
              </Link>
            </div>
          </div>
        </MobileNavMenu>
      </MobileNav>
    </Navbar>
  );
}
