"use client";
import { useState } from "react";
import Link from "next/link";
import Image from "next/image";
import { But<PERSON> } from "@/components/ui/button";
import {
  IconMenu2,
  IconX,
  IconPlus,
  IconMessage,
  IconLogout,
  IconUser,
  IconSettings,
} from "@tabler/icons-react";
import { ChatHistoryItem } from "@/types/dashboard";
import { handleSignOut } from "@/actions/signout";
import QueryProvider from "@/providers/query-provider";
import ReadingsCounter from "@/components/dashboard/ReadingsCounter";

interface DashboardWrapperProps {
  children: React.ReactNode;
  chatHistory?: ChatHistoryItem[];
  userEmail?: string;
  userName?: string;
  userImage?: string;
  onConversationClick?: (conversation: ChatHistoryItem) => void;
  onNewChatClick?: () => void;
}

export default function DashboardWrapper({
  children,
  chatHistory = [],
  userEmail = "<EMAIL>",
  userName = "المستخدم",
  userImage,
  onConversationClick,
  onNewChatClick,
}: DashboardWrapperProps) {
  const [sidebarOpen, setSidebarOpen] = useState(false); // Start closed on mobile

  // Default chat history if none provided (empty for now since we fetch from DB)
  const defaultChatHistory: ChatHistoryItem[] = [];

  const displayChatHistory =
    chatHistory.length > 0 ? chatHistory : defaultChatHistory;

  return (
    <QueryProvider>
      <div className="flex h-screen bg-gray-50 dark:bg-gray-900" dir="rtl">
        {/* Mobile Overlay */}
        {sidebarOpen && (
          <div
            className="fixed inset-0 bg-black/10 z-40 lg:hidden"
            onClick={() => setSidebarOpen(false)}
          />
        )}

        {/* Sidebar */}
        <div
          className={`${
            sidebarOpen ? "translate-x-0" : "translate-x-full"
          } fixed lg:static lg:translate-x-0 z-50 w-64 transition-transform duration-300 bg-gray-900 text-white flex flex-col h-full overflow-hidden right-0`}>
          {/* Sidebar Header */}
          <div className="p-3 sm:p-4 border-b border-gray-700">
            <div className="flex items-center justify-center lg:justify-center">
              <Link href="/" className="flex items-center gap-2">
                <Image
                  src="/ferjeni_logo.png"
                  alt="Fenjeni"
                  width={140}
                  height={45}
                  className="h-8 sm:h-10 lg:h-12 w-auto"
                />
              </Link>
              <Button
                variant="ghost"
                size="icon"
                onClick={() => setSidebarOpen(false)}
                className="text-gray-400 hover:text-white lg:hidden p-1 sm:p-2 absolute left-3">
                <IconX className="h-4 w-4 sm:h-5 sm:w-5" />
              </Button>
            </div>
          </div>

          {/* New Chat Button */}
          <div className="p-3 sm:p-4">
            <Button
              onClick={onNewChatClick}
              className="w-full bg-amber-600 hover:bg-amber-700 text-white flex items-center gap-2 transition-all duration-200 hover:scale-105 py-2.5 sm:py-3 text-sm sm:text-base">
              محادثة جديدة
              <IconPlus className="h-4 w-4 sm:h-5 sm:w-5" />
            </Button>
          </div>

          {/* Chat History */}
          <div className="flex-1 overflow-y-auto px-3 sm:px-4">
            <div className="space-y-1 sm:space-y-2">
              <h3 className="text-xs font-semibold text-gray-400 uppercase tracking-wider mb-2 sm:mb-3">
                المحادثات السابقة
              </h3>
              {displayChatHistory.length === 0 ? (
                <div className="text-center py-6 sm:py-8">
                  <IconMessage className="h-6 w-6 sm:h-8 sm:w-8 text-gray-500 mx-auto mb-2" />
                  <p className="text-xs sm:text-sm text-gray-500">
                    لا توجد محادثات سابقة
                  </p>
                </div>
              ) : (
                displayChatHistory.map((chat) => (
                  <div
                    key={chat.id}
                    onClick={() => onConversationClick?.(chat)}
                    className="flex items-center gap-2 sm:gap-3 p-2 sm:p-3 rounded-lg hover:bg-gray-800 cursor-pointer transition-colors group">
                    <IconMessage className="h-3 w-3 sm:h-4 sm:w-4 text-gray-400 flex-shrink-0" />
                    <div className="flex-1 min-w-0">
                      <p className="text-xs sm:text-sm font-medium text-white truncate">
                        {chat.title}
                      </p>
                      <p className="text-xs text-gray-400">{chat.date}</p>
                    </div>
                  </div>
                ))
              )}
            </div>
          </div>

          {/* Readings Counter */}
          <ReadingsCounter />

          {/* User Menu */}
          <div className="p-3 sm:p-4 border-t border-gray-700">
            <div className="flex items-center gap-2 sm:gap-3 p-2 sm:p-3 rounded-lg hover:bg-gray-800 cursor-pointer transition-colors">
              <div className="w-6 h-6 sm:w-8 sm:h-8 rounded-full overflow-hidden flex-shrink-0">
                {userImage ? (
                  <img
                    src={userImage}
                    alt={userName}
                    width={32}
                    height={32}
                    className="w-full h-full object-cover"
                  />
                ) : (
                  <div className="w-full h-full bg-amber-600 flex items-center justify-center">
                    <IconUser className="h-3 w-3 sm:h-4 sm:w-4 text-white" />
                  </div>
                )}
              </div>
              <div className="flex-1 min-w-0">
                <p className="text-xs sm:text-sm font-medium text-white truncate">
                  {userName}
                </p>
                <p className="text-xs text-gray-400 truncate">{userEmail}</p>
              </div>
            </div>

            <div className="mt-2 space-y-1">
              <Button
                variant="ghost"
                className="w-full justify-start text-gray-400 hover:text-white hover:bg-gray-800 py-2 text-xs sm:text-sm">
                الإعدادات
                <IconSettings className="h-3 w-3 sm:h-4 sm:w-4 ml-2" />
              </Button>
              <form action={handleSignOut}>
                <Button
                  type="submit"
                  variant="ghost"
                  className="w-full justify-start text-gray-400 hover:text-white hover:bg-gray-800 py-2 text-xs sm:text-sm">
                  تسجيل الخروج
                  <IconLogout className="h-3 w-3 sm:h-4 sm:w-4 ml-2" />
                </Button>
              </form>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="flex-1 flex flex-col">
          {/* Mobile Header */}
          <div className="lg:hidden bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 p-3 sm:p-4">
            <div className="flex items-center justify-between">
              <div className="w-8 sm:w-10" /> {/* Spacer */}
              <Link href="/">
                <Image
                  src="/ferjeni_logo.png"
                  alt="Fenjeni"
                  width={140}
                  height={45}
                  className="h-8 sm:h-10 w-auto"
                />
              </Link>
              <Button
                variant="ghost"
                size="icon"
                onClick={() => setSidebarOpen(true)}
                className="text-gray-600 dark:text-gray-400 p-1 sm:p-2">
                <IconMenu2 className="h-5 w-5 sm:h-6 sm:w-6" />
              </Button>
            </div>
          </div>

          {/* Dashboard Content */}
          <div className="flex-1 overflow-y-auto">{children}</div>
        </div>

        {/* Overlay for mobile */}
        {sidebarOpen && (
          <div
            className="fixed inset-0 bg-black/10 lg:hidden z-40"
            onClick={() => setSidebarOpen(false)}
          />
        )}
      </div>
    </QueryProvider>
  );
}
