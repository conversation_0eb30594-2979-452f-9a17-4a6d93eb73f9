"use server";

import prisma from "@/lib/prisma";

export async function seedPlans() {
  try {
    // Check if plans already exist
    const existingPlans = await prisma.plan.findMany();
    
    if (existingPlans.length > 0) {
      return { success: true, message: "Plans already exist" };
    }

    // Create plans (you'll need to replace these with actual Stripe price IDs)
    const plans = [
      {
        name: "Free Plan",
        nameAr: "الباقة المجانية",
        description: "Get started with one free coffee cup reading",
        descriptionAr: "ابدأ مع قراءة فنجان قهوة مجانية واحدة",
        price: 0,
        currency: "USD",
        interval: "lifetime",
        stripePriceId: "free_plan_no_stripe", // Not a real Stripe price ID
        features: [
          "1 free coffee cup reading",
          "Basic symbol analysis",
          "Instant results"
        ],
        featuresAr: [
          "قراءة فنجان قهوة مجانية واحدة",
          "تحليل أساسي للرموز",
          "نتائج فورية"
        ],
        readingsLimit: 1,
        isActive: true,
      },
      {
        name: "Starter Plan",
        nameAr: "باقة البداية",
        description: "Perfect for beginners with 5 coffee cup readings",
        descriptionAr: "مثالية للمبتدئين مع 5 قراءات فنجان قهوة",
        price: 3.00,
        currency: "USD",
        interval: "monthly",
        stripePriceId: "price_REPLACE_WITH_ACTUAL_STRIPE_PRICE_ID_1", // Replace with actual Stripe price ID
        features: [
          "5 coffee cup readings per month",
          "Basic symbol analysis",
          "Instant results",
          "Email support"
        ],
        featuresAr: [
          "5 قراءات فنجان قهوة شهرياً",
          "تحليل أساسي للرموز",
          "نتائج فورية",
          "دعم عبر البريد الإلكتروني"
        ],
        readingsLimit: 5,
        isActive: true,
      },
      {
        name: "Basic Plan",
        nameAr: "الباقة الأساسية",
        description: "Most popular plan with 10 detailed readings",
        descriptionAr: "الباقة الأكثر شعبية مع 10 قراءات مفصلة",
        price: 25.00,
        currency: "USD",
        interval: "monthly",
        stripePriceId: "price_REPLACE_WITH_ACTUAL_STRIPE_PRICE_ID_2", // Replace with actual Stripe price ID
        features: [
          "10 coffee cup readings per month",
          "Detailed symbol analysis",
          "Comprehensive interpretations",
          "24/7 support",
          "Save reading history",
          "Instant notifications"
        ],
        featuresAr: [
          "10 قراءات فنجان قهوة شهرياً",
          "تحليل مفصل للرموز",
          "تفسير شامل للمعاني",
          "دعم على مدار الساعة",
          "حفظ تاريخ القراءات",
          "إشعارات فورية"
        ],
        readingsLimit: 10,
        isActive: true,
      },
      {
        name: "Premium Plan",
        nameAr: "الباقة المتقدمة",
        description: "For professionals with unlimited readings",
        descriptionAr: "للمحترفين مع قراءات غير محدودة",
        price: 35.00,
        currency: "USD",
        interval: "monthly",
        stripePriceId: "price_REPLACE_WITH_ACTUAL_STRIPE_PRICE_ID_3", // Replace with actual Stripe price ID
        features: [
          "Unlimited coffee cup readings",
          "Advanced detailed analysis",
          "Comprehensive interpretations with predictions",
          "Complete reading history",
          "Monthly detailed reports",
          "Direct personal consultations",
          "Priority support"
        ],
        featuresAr: [
          "قراءات فنجان قهوة غير محدودة",
          "تحليل متقدم ومفصل",
          "تفسير شامل مع التوقعات",
          "تاريخ كامل للقراءات",
          "تقارير شهرية مفصلة",
          "استشارات شخصية مباشرة",
          "دعم ذو أولوية"
        ],
        readingsLimit: 999999, // Effectively unlimited
        isActive: true,
      },
    ];

    // Create all plans
    const createdPlans = await Promise.all(
      plans.map(plan => prisma.plan.create({ data: plan }))
    );

    return { 
      success: true, 
      message: `Created ${createdPlans.length} plans successfully`,
      plans: createdPlans 
    };
  } catch (error) {
    console.error("Error seeding plans:", error);
    return { 
      success: false, 
      error: "Failed to seed plans" 
    };
  }
}
